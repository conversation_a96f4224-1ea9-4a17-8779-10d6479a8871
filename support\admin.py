from django.contrib import admin
from .models import SupportTicket, TicketMessage


class TicketMessageInline(admin.TabularInline):
    """
    Inline admin for ticket messages
    """
    model = TicketMessage
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('sender', 'message', 'is_internal', 'attachment', 'created_at')


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    """
    Support ticket admin interface
    """

    list_display = ('ticket_number', 'title', 'user', 'category', 'priority', 'status', 'assigned_to', 'created_at')
    list_filter = ('category', 'priority', 'status', 'created_at', 'assigned_to')
    search_fields = ('ticket_number', 'title', 'user__username', 'user__email')
    ordering = ('-created_at',)

    fieldsets = (
        ('Ticket Information', {
            'fields': ('ticket_number', 'title', 'description', 'user')
        }),
        ('Classification', {
            'fields': ('category', 'priority', 'status', 'assigned_to')
        }),
        ('Related Information', {
            'fields': ('device_related', 'attachments'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'resolved_at', 'closed_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('ticket_number', 'created_at', 'updated_at', 'resolved_at', 'closed_at')
    inlines = [TicketMessageInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'assigned_to', 'device_related')


@admin.register(TicketMessage)
class TicketMessageAdmin(admin.ModelAdmin):
    """
    Ticket message admin interface
    """

    list_display = ('ticket', 'sender', 'message_preview', 'is_internal', 'created_at')
    list_filter = ('is_internal', 'created_at')
    search_fields = ('ticket__ticket_number', 'sender__username', 'message')
    ordering = ('-created_at',)

    fieldsets = (
        ('Message Details', {
            'fields': ('ticket', 'sender', 'message', 'is_internal')
        }),
        ('Attachment', {
            'fields': ('attachment',),
            'classes': ('collapse',)
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )

    readonly_fields = ('created_at',)

    def message_preview(self, obj):
        """Show a preview of the message"""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = 'Message Preview'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('ticket', 'sender')
