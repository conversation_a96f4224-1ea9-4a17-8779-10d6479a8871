from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Notifications
    path('notifications/', views.NotificationListCreateAPIView.as_view(), name='notification_list_create'),
    path('notifications/<uuid:pk>/', views.NotificationDetailAPIView.as_view(), name='notification_detail'),
    path('notifications/<uuid:notification_id>/read/', views.mark_notification_read_api, name='mark_notification_read'),
    path('notifications/bulk-action/', views.bulk_notification_action_api, name='bulk_notification_action'),
    
    # System logs (admin only)
    path('logs/', views.SystemLogListAPIView.as_view(), name='system_logs'),
    
    # Statistics
    path('stats/', views.notification_stats_api, name='notification_stats'),
]
