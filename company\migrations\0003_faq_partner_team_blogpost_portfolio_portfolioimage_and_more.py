# Generated by Django 5.2.4 on 2025-07-29 04:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0002_sitesettings'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_ar', models.CharField(max_length=300, verbose_name='السؤال (عربي)')),
                ('question_en', models.CharField(blank=True, max_length=300, verbose_name='السؤال (إنجليزي)')),
                ('answer_ar', models.TextField(verbose_name='الإجابة (عربي)')),
                ('answer_en', models.TextField(blank=True, verbose_name='الإجابة (إنجليزي)')),
                ('category', models.CharField(blank=True, max_length=50, verbose_name='الفئة')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('is_published', models.BooleanField(default=True, verbose_name='منشور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'سؤال شائع',
                'verbose_name_plural': 'الأسئلة الشائعة',
                'db_table': 'faq',
                'ordering': ['display_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Partner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=100, verbose_name='اسم الشريك (عربي)')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='اسم الشريك (إنجليزي)')),
                ('logo', models.ImageField(upload_to='partners/', verbose_name='الشعار')),
                ('website_url', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('description_ar', models.TextField(blank=True, verbose_name='الوصف (عربي)')),
                ('description_en', models.TextField(blank=True, verbose_name='الوصف (إنجليزي)')),
                ('partner_type', models.CharField(choices=[('client', 'عميل'), ('supplier', 'مورد'), ('partner', 'شريك'), ('sponsor', 'راعي')], default='client', max_length=20, verbose_name='نوع الشراكة')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('show_on_homepage', models.BooleanField(default=False, verbose_name='عرض في الصفحة الرئيسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'شريك',
                'verbose_name_plural': 'الشركاء والعملاء',
                'db_table': 'partners',
                'ordering': ['display_order', 'name_ar'],
            },
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_ar', models.CharField(max_length=100, verbose_name='الاسم (عربي)')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم (إنجليزي)')),
                ('position_ar', models.CharField(max_length=100, verbose_name='المنصب (عربي)')),
                ('position_en', models.CharField(blank=True, max_length=100, verbose_name='المنصب (إنجليزي)')),
                ('bio_ar', models.TextField(blank=True, verbose_name='نبذة شخصية (عربي)')),
                ('bio_en', models.TextField(blank=True, verbose_name='نبذة شخصية (إنجليزي)')),
                ('photo', models.ImageField(upload_to='team/', verbose_name='الصورة الشخصية')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('linkedin_url', models.URLField(blank=True, verbose_name='لينكد إن')),
                ('twitter_url', models.URLField(blank=True, verbose_name='تويتر')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عضو فريق',
                'verbose_name_plural': 'فريق العمل',
                'db_table': 'team',
                'ordering': ['display_order', 'name_ar'],
            },
        ),
        migrations.CreateModel(
            name='BlogPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=200, verbose_name='عنوان المقال (عربي)')),
                ('title_en', models.CharField(blank=True, max_length=200, verbose_name='عنوان المقال (إنجليزي)')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='الرابط المختصر')),
                ('excerpt_ar', models.TextField(max_length=300, verbose_name='مقتطف (عربي)')),
                ('excerpt_en', models.TextField(blank=True, max_length=300, verbose_name='مقتطف (إنجليزي)')),
                ('content_ar', models.TextField(verbose_name='المحتوى (عربي)')),
                ('content_en', models.TextField(blank=True, verbose_name='المحتوى (إنجليزي)')),
                ('featured_image', models.ImageField(upload_to='blog/', verbose_name='الصورة الرئيسية')),
                ('category', models.CharField(blank=True, max_length=50, verbose_name='الفئة')),
                ('tags', models.CharField(blank=True, max_length=200, verbose_name='الكلمات المفتاحية')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مقال مميز')),
                ('is_published', models.BooleanField(default=True, verbose_name='منشور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ النشر')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='الكاتب')),
            ],
            options={
                'verbose_name': 'مقال',
                'verbose_name_plural': 'المقالات والأخبار',
                'db_table': 'blog_posts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Portfolio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_ar', models.CharField(max_length=200, verbose_name='عنوان المشروع (عربي)')),
                ('title_en', models.CharField(blank=True, max_length=200, verbose_name='عنوان المشروع (إنجليزي)')),
                ('description_ar', models.TextField(verbose_name='وصف المشروع (عربي)')),
                ('description_en', models.TextField(blank=True, verbose_name='وصف المشروع (إنجليزي)')),
                ('category', models.CharField(choices=[('smart_home', 'منزل ذكي كامل'), ('security', 'أنظمة الأمان'), ('lighting', 'أنظمة الإضاءة'), ('climate', 'التحكم في المناخ'), ('entertainment', 'أنظمة الترفيه'), ('automation', 'الأتمتة المنزلية'), ('other', 'أخرى')], default='smart_home', max_length=20, verbose_name='فئة المشروع')),
                ('client_name', models.CharField(blank=True, max_length=100, verbose_name='اسم العميل')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('project_date', models.DateField(blank=True, null=True, verbose_name='تاريخ المشروع')),
                ('duration', models.CharField(blank=True, max_length=50, verbose_name='مدة التنفيذ')),
                ('media_type', models.CharField(choices=[('image', 'صورة'), ('video', 'مقطع فيديو'), ('gallery', 'معرض صور'), ('360_view', 'عرض 360 درجة')], default='image', max_length=20, verbose_name='نوع الوسائط')),
                ('featured_image', models.ImageField(upload_to='portfolio/images/', verbose_name='الصورة الرئيسية')),
                ('video_url', models.URLField(blank=True, verbose_name='رابط الفيديو (YouTube/Vimeo)')),
                ('video_file', models.FileField(blank=True, upload_to='portfolio/videos/', verbose_name='ملف الفيديو')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مشروع مميز')),
                ('is_published', models.BooleanField(default=True, verbose_name='منشور')),
                ('show_on_homepage', models.BooleanField(default=False, verbose_name='عرض في الصفحة الرئيسية')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('technologies_used', models.TextField(blank=True, verbose_name='التقنيات المستخدمة')),
                ('project_cost', models.CharField(blank=True, max_length=100, verbose_name='تكلفة المشروع')),
                ('client_feedback', models.TextField(blank=True, verbose_name='تقييم العميل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='company.service', verbose_name='الخدمة المرتبطة')),
            ],
            options={
                'verbose_name': 'مشروع في المعرض',
                'verbose_name_plural': 'معرض الأعمال',
                'db_table': 'portfolio',
                'ordering': ['-display_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PortfolioImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='portfolio/gallery/', verbose_name='الصورة')),
                ('caption_ar', models.CharField(blank=True, max_length=200, verbose_name='وصف الصورة (عربي)')),
                ('caption_en', models.CharField(blank=True, max_length=200, verbose_name='وصف الصورة (إنجليزي)')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('portfolio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='company.portfolio', verbose_name='المشروع')),
            ],
            options={
                'verbose_name': 'صورة المشروع',
                'verbose_name_plural': 'صور المشاريع',
                'db_table': 'portfolio_images',
                'ordering': ['display_order'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('client_position', models.CharField(blank=True, max_length=100, verbose_name='المنصب/الوظيفة')),
                ('client_company', models.CharField(blank=True, max_length=100, verbose_name='الشركة')),
                ('client_image', models.ImageField(blank=True, upload_to='testimonials/', verbose_name='صورة العميل')),
                ('testimonial_ar', models.TextField(verbose_name='الرأي (عربي)')),
                ('testimonial_en', models.TextField(blank=True, verbose_name='الرأي (إنجليزي)')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='التقييم (1-5)')),
                ('is_featured', models.BooleanField(default=False, verbose_name='رأي مميز')),
                ('is_published', models.BooleanField(default=True, verbose_name='منشور')),
                ('show_on_homepage', models.BooleanField(default=False, verbose_name='عرض في الصفحة الرئيسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('portfolio', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='company.portfolio', verbose_name='المشروع المرتبط')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='company.service', verbose_name='الخدمة المرتبطة')),
            ],
            options={
                'verbose_name': 'رأي العميل',
                'verbose_name_plural': 'آراء العملاء',
                'db_table': 'testimonials',
                'ordering': ['-created_at'],
            },
        ),
    ]
