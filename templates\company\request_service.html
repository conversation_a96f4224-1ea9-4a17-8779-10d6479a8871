<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب خدمة - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Arabic Luxury CSS -->
    <link href="/static/css/arabic-luxury.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-luxury">
        <div class="container">
            <a class="navbar-brand navbar-brand-luxury" href="/">
                <i class="fas fa-home me-2"></i>IQHome
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/services/">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#contact">تواصل معنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- رأس الصفحة -->
    <section class="py-5 bg-pattern">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="text-gradient mb-4">اطلب خدمتك الآن</h1>
                    <p class="lead">
                        املأ النموذج أدناه وسيتواصل معك أحد خبرائنا خلال 24 ساعة
                        لتقديم أفضل الحلول التقنية لمنزلك
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- نموذج طلب الخدمة -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <!-- عرض الرسائل -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="form-luxury">
                        <div class="text-center mb-4">
                            <div class="service-icon mx-auto mb-3">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <h2>نموذج طلب الخدمة</h2>
                            <p class="text-muted">جميع الحقول المطلوبة مميزة بعلامة *</p>
                        </div>
                        
                        <form method="post" id="serviceRequestForm">
                            {% csrf_token %}
                            
                            <!-- معلومات الخدمة -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-cogs me-2"></i>معلومات الخدمة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label form-label-luxury">نوع الخدمة المطلوبة *</label>
                                        <select class="form-control form-control-luxury" name="service" required>
                                            <option value="">اختر نوع الخدمة</option>
                                            {% for service in services %}
                                                <option value="{{ service.id }}" 
                                                    {% if request.GET.service == service.id|stringformat:"s" %}selected{% endif %}>
                                                    {{ service.name_ar }} - {{ service.price_range_display }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label form-label-luxury">وصف تفصيلي للطلب *</label>
                                        <textarea class="form-control form-control-luxury" name="description" rows="4" 
                                                  placeholder="اكتب تفاصيل ما تحتاجه، حجم المنزل، عدد الغرف، المتطلبات الخاصة..." required></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العميل -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-user me-2"></i>معلومات العميل</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">الاسم الكامل *</label>
                                            <input type="text" class="form-control form-control-luxury" name="customer_name" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">رقم الهاتف *</label>
                                            <input type="tel" class="form-control form-control-luxury" name="customer_phone" 
                                                   placeholder="+964 ************" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">البريد الإلكتروني</label>
                                            <input type="email" class="form-control form-control-luxury" name="customer_email">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">المدينة *</label>
                                            <select class="form-control form-control-luxury" name="customer_city" required>
                                                <option value="">اختر المدينة</option>
                                                <option value="بغداد">بغداد</option>
                                                <option value="البصرة">البصرة</option>
                                                <option value="أربيل">أربيل</option>
                                                <option value="الموصل">الموصل</option>
                                                <option value="النجف">النجف</option>
                                                <option value="كربلاء">كربلاء</option>
                                                <option value="السليمانية">السليمانية</option>
                                                <option value="الديوانية">الديوانية</option>
                                                <option value="الناصرية">الناصرية</option>
                                                <option value="الكوت">الكوت</option>
                                                <option value="الرمادي">الرمادي</option>
                                                <option value="تكريت">تكريت</option>
                                                <option value="أخرى">أخرى</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label form-label-luxury">العنوان التفصيلي</label>
                                            <textarea class="form-control form-control-luxury" name="customer_address" rows="2" 
                                                      placeholder="المنطقة، الحي، رقم الدار..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل الموعد -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar me-2"></i>تفاصيل الموعد</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">التاريخ المفضل للزيارة</label>
                                            <input type="date" class="form-control form-control-luxury" name="preferred_date" 
                                                   min="{{ today|date:'Y-m-d' }}">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label form-label-luxury">الوقت المفضل</label>
                                            <select class="form-control form-control-luxury" name="preferred_time">
                                                <option value="">اختر الوقت</option>
                                                <option value="09:00">صباحاً (9:00 - 12:00)</option>
                                                <option value="13:00">بعد الظهر (1:00 - 4:00)</option>
                                                <option value="16:00">مساءً (4:00 - 7:00)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظات إضافية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-sticky-note me-2"></i>ملاحظات إضافية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label form-label-luxury">ملاحظات أو متطلبات خاصة</label>
                                        <textarea class="form-control form-control-luxury" name="customer_notes" rows="3" 
                                                  placeholder="أي ملاحظات أو متطلبات خاصة تود إضافتها..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- شروط الخدمة -->
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" required>
                                        <label class="form-check-label" for="terms">
                                            أوافق على <a href="#" class="text-primary">شروط الخدمة</a> و 
                                            <a href="#" class="text-primary">سياسة الخصوصية</a> *
                                        </label>
                                    </div>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="marketing">
                                        <label class="form-check-label" for="marketing">
                                            أوافق على تلقي العروض والتحديثات عبر البريد الإلكتروني والرسائل النصية
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار الإرسال -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-luxury btn-lg me-3">
                                    <i class="fas fa-paper-plane me-2"></i>إرسال الطلب
                                </button>
                                <a href="/services/" class="btn btn-elegant btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>العودة للخدمات
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- معلومات جانبية -->
                <div class="col-lg-4">
                    <div class="card-luxury p-4 mb-4">
                        <h5 class="text-gradient mb-3">
                            <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                        </h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                استشارة مجانية لجميع العملاء
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                رد خلال 24 ساعة كحد أقصى
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                زيارة مجانية لتقييم المشروع
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                عرض سعر مفصل ومجاني
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                ضمان على جميع الخدمات
                            </li>
                        </ul>
                    </div>

                    <div class="card-luxury p-4">
                        <h5 class="text-gradient mb-3">
                            <i class="fas fa-headset me-2"></i>تحتاج مساعدة؟
                        </h5>
                        <p class="mb-3">فريق خدمة العملاء متاح لمساعدتك</p>
                        <div class="d-grid gap-2">
                            <a href="tel:+9647701234567" class="btn btn-elegant">
                                <i class="fas fa-phone me-2"></i>اتصل بنا
                            </a>
                            <a href="https://wa.me/9647701234567" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer-luxury">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2025 IQHome. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تحديد التاريخ الأدنى لليوم الحالي
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="preferred_date"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.setAttribute('min', today);
            }
        });

        // التحقق من صحة النموذج
        document.getElementById('serviceRequestForm').addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            // إظهار رسالة التحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
            submitBtn.disabled = true;
        });

        // تنسيق رقم الهاتف
        document.querySelector('input[name="customer_phone"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('964')) {
                value = '+' + value;
            } else if (value.startsWith('0')) {
                value = '+964' + value.substring(1);
            } else if (value.length > 0 && !value.startsWith('+964')) {
                value = '+964' + value;
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
