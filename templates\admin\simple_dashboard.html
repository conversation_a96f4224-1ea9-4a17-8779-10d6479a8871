<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="#">
                <i class="fas fa-shield-alt me-2"></i>IQHome - لوحة التحكم
            </a>

            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}" class="active">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                            {% if pending_requests > 0 %}
                                <span class="badge-warning ms-auto">{{ pending_requests }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                            {% if unread_messages > 0 %}
                                <span class="badge-danger ms-auto">{{ unread_messages }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_portfolio' %}">
                            <i class="fas fa-images"></i>
                            معرض الأعمال
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_testimonials' %}">
                            <i class="fas fa-star"></i>
                            آراء العملاء
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_content' %}">
                            <i class="fas fa-file-alt"></i>
                            إدارة المحتوى
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}">
                            <i class="fas fa-cog"></i>
                            إعدادات الموقع
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس لوحة التحكم الاحترافي -->
                <div class="card-dark-pro">
                    <h1 class="card-title-dark">مرحباً بك في لوحة التحكم</h1>
                    <p class="text-secondary-dark">إدارة شاملة لجميع عمليات IQHome</p>
                </div>

                <!-- بطاقات الإحصائيات الاحترافية -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_services }}</div>
                            <div class="stat-label-dark">إجمالي الخدمات</div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_requests }}</div>
                            <div class="stat-label-dark">طلبات الخدمات</div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_users }}</div>
                            <div class="stat-label-dark">العملاء المسجلين</div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_messages }}</div>
                            <div class="stat-label-dark">رسائل التواصل</div>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- الطلبات الحديثة الاحترافية -->
                    <div class="col-lg-8">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h3 class="card-title-dark">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    الطلبات الحديثة
                                </h3>
                            </div>

                            <div class="table-responsive">
                                <table class="table-dark-pro">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>العميل</th>
                                            <th>الخدمة</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for request in recent_requests|slice:":5" %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'company:admin_request_detail' request.id %}" class="text-accent text-decoration-none">
                                                    {{ request.request_number }}
                                                </a>
                                            </td>
                                            <td>{{ request.customer_name }}</td>
                                            <td>{{ request.service.name_ar }}</td>
                                            <td>
                                                {% if request.status == 'pending' %}
                                                    <span class="badge-warning">{{ request.get_status_display }}</span>
                                                {% elif request.status == 'completed' %}
                                                    <span class="badge-success">{{ request.get_status_display }}</span>
                                                {% else %}
                                                    <span class="badge-primary">{{ request.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ request.created_at|date:"d/m/Y" }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted-dark">لا توجد طلبات</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <div class="text-center mt-3">
                                <a href="{% url 'company:admin_requests' %}" class="btn-outline-dark">
                                    عرض جميع الطلبات
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- الرسائل الحديثة الاحترافية -->
                    <div class="col-lg-4">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h3 class="card-title-dark">
                                    <i class="fas fa-envelope me-2"></i>
                                    الرسائل الحديثة
                                </h3>
                            </div>

                            {% for message in recent_messages|slice:":5" %}
                            <div class="d-flex align-items-start mb-3 pb-3" style="border-bottom: 1px solid var(--border-color);">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 text-primary-dark">{{ message.name }}</h6>
                                    <p class="mb-1 text-secondary-dark small">{{ message.subject|truncatechars:40 }}</p>
                                    <small class="text-muted-dark">{{ message.created_at|timesince }}</small>
                                </div>
                                {% if not message.is_read %}
                                    <span class="badge-danger">جديد</span>
                                {% endif %}
                            </div>
                            {% empty %}
                            <p class="text-center text-muted-dark">لا توجد رسائل</p>
                            {% endfor %}

                            <div class="text-center mt-3">
                                <a href="{% url 'company:admin_messages' %}" class="btn-outline-dark">
                                    عرض جميع الرسائل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الخدمات الأكثر طلباً الاحترافية -->
                <div class="row g-4 mt-4">
                    <div class="col-12">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h3 class="card-title-dark">
                                    <i class="fas fa-star me-2"></i>
                                    الخدمات الأكثر طلباً
                                </h3>
                            </div>
                            <div class="row">
                                {% for service in popular_services|slice:":4" %}
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="text-center">
                                        <div class="stat-number-dark">{{ service.request_count }}</div>
                                        <div class="stat-label-dark">{{ service.name_ar }}</div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
</body>
</html>
