<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خدماتنا - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Arabic Luxury CSS -->
    <link href="/static/css/arabic-luxury.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-luxury">
        <div class="container">
            <a class="navbar-brand navbar-brand-luxury" href="/">
                <i class="fas fa-home me-2"></i>IQHome
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury active" href="/services/">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#contact">تواصل معنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="/request-service/" class="btn btn-luxury me-2">
                        <i class="fas fa-phone me-2"></i>اطلب خدمة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس الصفحة -->
    <section class="py-5 bg-pattern">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="text-gradient mb-4">خدماتنا المتميزة</h1>
                    <p class="lead">
                        نقدم مجموعة شاملة من الخدمات المتخصصة في تقنيات المنازل الذكية
                        لتحويل منزلك إلى تحفة تقنية عصرية
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- الخدمات المميزة -->
    {% if featured_services %}
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2>الخدمات المميزة</h2>
                    <p class="text-muted">أكثر خدماتنا طلباً وتميزاً</p>
                </div>
            </div>
            <div class="row g-4">
                {% for service in featured_services %}
                <div class="col-lg-4 col-md-6">
                    <div class="card-luxury h-100">
                        <div class="card-body p-4">
                            <div class="service-icon mx-auto mb-3">
                                {% if service.category == 'installation' %}
                                    <i class="fas fa-home"></i>
                                {% elif service.category == 'maintenance' %}
                                    <i class="fas fa-tools"></i>
                                {% elif service.category == 'consultation' %}
                                    <i class="fas fa-user-tie"></i>
                                {% else %}
                                    <i class="fas fa-cogs"></i>
                                {% endif %}
                            </div>
                            <h4 class="text-center mb-3">{{ service.name_ar }}</h4>
                            <p class="text-muted text-center">{{ service.description_ar|truncatewords:20 }}</p>
                            
                            <div class="text-center mb-3">
                                <span class="badge bg-warning text-dark">{{ service.get_category_display }}</span>
                            </div>
                            
                            <div class="text-center mb-3">
                                <h5 class="text-gradient">{{ service.price_range_display }}</h5>
                                <small class="text-muted">مدة التنفيذ: {{ service.duration_hours }} ساعة</small>
                            </div>
                            
                            <div class="text-center">
                                <a href="{% url 'company:service_detail' service.id %}" class="btn btn-elegant me-2">
                                    <i class="fas fa-info-circle me-2"></i>التفاصيل
                                </a>
                                <a href="/request-service/?service={{ service.id }}" class="btn btn-luxury">
                                    <i class="fas fa-phone me-2"></i>اطلب الآن
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}

    <!-- جميع الخدمات -->
    <section class="py-5 services-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2>جميع خدماتنا</h2>
                    <p class="text-muted">اختر الخدمة التي تناسب احتياجاتك</p>
                </div>
            </div>
            
            <!-- فلترة الخدمات -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary active" data-filter="all">
                            جميع الخدمات
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-filter="installation">
                            التركيب
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-filter="maintenance">
                            الصيانة
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-filter="consultation">
                            الاستشارات
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-filter="training">
                            التدريب
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="row g-4" id="services-grid">
                {% for service in services %}
                <div class="col-lg-4 col-md-6 service-item" data-category="{{ service.category }}">
                    <div class="service-card">
                        <div class="service-icon">
                            {% if service.category == 'installation' %}
                                <i class="fas fa-home"></i>
                            {% elif service.category == 'maintenance' %}
                                <i class="fas fa-tools"></i>
                            {% elif service.category == 'consultation' %}
                                <i class="fas fa-user-tie"></i>
                            {% elif service.category == 'training' %}
                                <i class="fas fa-graduation-cap"></i>
                            {% else %}
                                <i class="fas fa-cogs"></i>
                            {% endif %}
                        </div>
                        <h4>{{ service.name_ar }}</h4>
                        <p>{{ service.description_ar|truncatewords:15 }}</p>
                        
                        <div class="mb-3">
                            <span class="badge bg-secondary">{{ service.get_category_display }}</span>
                            {% if service.is_featured %}
                                <span class="badge bg-warning text-dark">مميزة</span>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <strong class="text-gradient">{{ service.price_range_display }}</strong>
                            <br>
                            <small class="text-muted">مدة التنفيذ: {{ service.duration_hours }} ساعة</small>
                        </div>
                        
                        <div class="d-flex gap-2 justify-content-center">
                            <a href="{% url 'company:service_detail' service.id %}" class="btn btn-elegant btn-sm">
                                التفاصيل
                            </a>
                            <a href="/request-service/?service={{ service.id }}" class="btn btn-luxury btn-sm">
                                اطلب الخدمة
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- دعوة للعمل -->
    <section class="py-5 bg-pattern">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2>هل تحتاج استشارة مجانية؟</h2>
                    <p class="lead mb-4">
                        فريقنا من الخبراء جاهز لمساعدتك في اختيار أفضل الحلول التقنية لمنزلك
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="/request-service/" class="btn btn-luxury">
                            <i class="fas fa-phone me-2"></i>احجز استشارة مجانية
                        </a>
                        <a href="/contact/" class="btn btn-elegant">
                            <i class="fas fa-envelope me-2"></i>تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer-luxury">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h4 class="text-gradient">IQHome</h4>
                    <p>الشركة الرائدة في تقنيات المنازل الذكية في العراق</p>
                </div>
                <div class="col-lg-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-decoration-none text-light">الرئيسية</a></li>
                        <li><a href="/services/" class="text-decoration-none text-light">خدماتنا</a></li>
                        <li><a href="/contact/" class="text-decoration-none text-light">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5>معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-phone me-2 text-gradient"></i>+964 ************</li>
                        <li><i class="fas fa-envelope me-2 text-gradient"></i><EMAIL></li>
                        <li><i class="fas fa-map-marker-alt me-2 text-gradient"></i>بغداد - الكرادة</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2025 IQHome. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // فلترة الخدمات
        document.querySelectorAll('[data-filter]').forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // تحديث الأزرار النشطة
                document.querySelectorAll('[data-filter]').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');
                
                // فلترة العناصر
                document.querySelectorAll('.service-item').forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                        item.classList.add('fade-in');
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // تأثيرات التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.service-card, .card-luxury').forEach(el => {
            el.classList.add('scroll-reveal');
            observer.observe(el);
        });
    </script>
</body>
</html>
