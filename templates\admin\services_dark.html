<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الخدمات - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>IQHome - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}" class="active">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-cogs me-2"></i>إدارة الخدمات
                            </h1>
                            <p class="text-secondary-dark">إدارة وتحرير جميع خدمات الشركة</p>
                        </div>
                        <div>
                            <a href="{% url 'company:admin_service_add' %}" class="btn-dark-pro">
                                <i class="fas fa-plus"></i>إضافة خدمة جديدة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_services }}</div>
                            <div class="stat-label-dark">إجمالي الخدمات</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-number-dark">{{ featured_services }}</div>
                            <div class="stat-label-dark">خدمات مميزة</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-number-dark">{{ active_services }}</div>
                            <div class="stat-label-dark">خدمات نشطة</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_requests }}</div>
                            <div class="stat-label-dark">إجمالي الطلبات</div>
                        </div>
                    </div>
                </div>

                <!-- فلترة وبحث -->
                <div class="card-dark-pro mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-group-dark">
                                <label class="form-label-dark">البحث في الخدمات</label>
                                <input type="text" class="form-control-dark" id="searchServices" placeholder="ابحث بالاسم أو الوصف...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group-dark">
                                <label class="form-label-dark">فلترة حسب الفئة</label>
                                <select class="form-control-dark" id="filterCategory">
                                    <option value="">جميع الفئات</option>
                                    <option value="installation">التركيب</option>
                                    <option value="maintenance">الصيانة</option>
                                    <option value="consultation">الاستشارات</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group-dark">
                                <label class="form-label-dark">فلترة حسب الحالة</label>
                                <select class="form-control-dark" id="filterStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="featured">مميز</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">&nbsp;</label>
                                <button class="btn-outline-dark w-100" onclick="clearFilters()">
                                    <i class="fas fa-times"></i>مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الخدمات -->
                <div class="card-dark-pro">
                    <div class="card-header-dark">
                        <h3 class="card-title-dark">
                            <i class="fas fa-list me-2"></i>قائمة الخدمات
                        </h3>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table-dark-pro" id="servicesTable">
                            <thead>
                                <tr>
                                    <th>الخدمة</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>عدد الطلبات</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in services %}
                                <tr data-category="{{ service.category }}" data-status="{% if service.is_active %}active{% else %}inactive{% endif %}{% if service.is_featured %} featured{% endif %}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="service-icon-dark me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                {% if service.category == 'installation' %}
                                                    <i class="fas fa-home"></i>
                                                {% elif service.category == 'maintenance' %}
                                                    <i class="fas fa-tools"></i>
                                                {% elif service.category == 'consultation' %}
                                                    <i class="fas fa-user-tie"></i>
                                                {% else %}
                                                    <i class="fas fa-cogs"></i>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <h6 class="text-primary-dark mb-1">{{ service.name_ar }}</h6>
                                                <small class="text-secondary-dark">{{ service.description_ar|truncatechars:50 }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge-secondary">{{ service.get_category_display }}</span>
                                    </td>
                                    <td>
                                        <strong class="text-accent">{{ service.price_range_display }}</strong>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column gap-1">
                                            {% if service.is_active %}
                                                <span class="badge-success">نشط</span>
                                            {% else %}
                                                <span class="badge-secondary">غير نشط</span>
                                            {% endif %}
                                            {% if service.is_featured %}
                                                <span class="badge-warning">مميز</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-accent">{{ service.requests.count }}</span>
                                    </td>
                                    <td>
                                        <small class="text-secondary-dark">{{ service.created_at|date:"d/m/Y" }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn-outline-dark btn-sm" onclick="editService('{{ service.id }}')" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-outline-dark btn-sm" onclick="toggleServiceStatus('{{ service.id }}')" title="تغيير الحالة">
                                                <i class="fas fa-{% if service.is_active %}eye-slash{% else %}eye{% endif %}"></i>
                                            </button>
                                            <button class="btn-outline-dark btn-sm" onclick="toggleFeatured('{{ service.id }}')" title="تمييز">
                                                <i class="fas fa-star{% if not service.is_featured %}-o{% endif %}"></i>
                                            </button>
                                            <button class="btn-outline-dark btn-sm text-danger" onclick="deleteService('{{ service.id }}')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted-dark py-4">
                                        <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                                        <br>لا توجد خدمات مضافة بعد
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة خدمة جديدة -->
    <div class="modal fade" id="addServiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--charcoal-medium); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title text-primary-dark">
                        <i class="fas fa-plus me-2"></i>إضافة خدمة جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <form id="addServiceForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">اسم الخدمة (عربي) *</label>
                                    <input type="text" class="form-control-dark" name="name_ar" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">اسم الخدمة (إنجليزي)</label>
                                    <input type="text" class="form-control-dark" name="name_en">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group-dark">
                            <label class="form-label-dark">وصف الخدمة (عربي) *</label>
                            <textarea class="form-control-dark" name="description_ar" rows="3" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">فئة الخدمة *</label>
                                    <select class="form-control-dark" name="category" required>
                                        <option value="">اختر الفئة...</option>
                                        <option value="installation">التركيب</option>
                                        <option value="maintenance">الصيانة</option>
                                        <option value="consultation">الاستشارات</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">نطاق السعر</label>
                                    <input type="text" class="form-control-dark" name="price_range" placeholder="مثال: 100-500 دولار">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                    <label class="form-check-label text-secondary-dark" for="is_active">
                                        خدمة نشطة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                    <label class="form-check-label text-secondary-dark" for="is_featured">
                                        خدمة مميزة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn-secondary-dark" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn-dark-pro" onclick="saveService()">
                        <i class="fas fa-save"></i>حفظ الخدمة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript خاص بإدارة الخدمات -->
    <script>
        // البحث والفلترة
        document.getElementById('searchServices').addEventListener('input', filterServices);
        document.getElementById('filterCategory').addEventListener('change', filterServices);
        document.getElementById('filterStatus').addEventListener('change', filterServices);
        
        function filterServices() {
            const searchTerm = document.getElementById('searchServices').value.toLowerCase();
            const categoryFilter = document.getElementById('filterCategory').value;
            const statusFilter = document.getElementById('filterStatus').value;
            const rows = document.querySelectorAll('#servicesTable tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const category = row.dataset.category;
                const status = row.dataset.status;
                
                const matchesSearch = text.includes(searchTerm);
                const matchesCategory = !categoryFilter || category === categoryFilter;
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                
                if (matchesSearch && matchesCategory && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchServices').value = '';
            document.getElementById('filterCategory').value = '';
            document.getElementById('filterStatus').value = '';
            filterServices();
        }
        
        // وظائف إدارة الخدمات
        function editService(serviceId) {
            // TODO: تنفيذ تعديل الخدمة
            showNotification('سيتم تنفيذ تعديل الخدمة قريباً', 'info');
        }
        
        function toggleServiceStatus(serviceId) {
            // TODO: تنفيذ تغيير حالة الخدمة
            showNotification('سيتم تنفيذ تغيير حالة الخدمة قريباً', 'info');
        }
        
        function toggleFeatured(serviceId) {
            // TODO: تنفيذ تمييز الخدمة
            showNotification('سيتم تنفيذ تمييز الخدمة قريباً', 'info');
        }
        
        function deleteService(serviceId) {
            if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
                // TODO: تنفيذ حذف الخدمة
                showNotification('سيتم تنفيذ حذف الخدمة قريباً', 'warning');
            }
        }
        
        function saveService() {
            const form = document.getElementById('addServiceForm');
            const formData = new FormData(form);
            
            // TODO: إرسال البيانات للخادم
            showNotification('سيتم تنفيذ حفظ الخدمة قريباً', 'info');
            
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addServiceModal'));
            modal.hide();
        }
    </script>
</body>
</html>
