from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
import json

from .models import Service, ServiceRequest, ContactMessage, SiteSettings, Portfolio, Testimonial, FAQ, BlogPost, Team, Partner
from accounts.models import User


def is_admin(user):
    """التحقق من كون المستخدم إداري"""
    return user.is_authenticated and (user.is_staff or user.role == 'admin')


def admin_login_view(request):
    """صفحة تسجيل دخول الإدارة"""
    if request.user.is_authenticated and is_admin(request.user):
        return redirect('company:admin_dashboard')
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user and is_admin(user):
            login(request, user)
            messages.success(request, f'مرحباً بك في مركز التحكم، {user.get_full_name()}')
            return redirect('company:admin_dashboard')
        else:
            messages.error(request, 'بيانات تسجيل الدخول غير صحيحة أو ليس لديك صلاحيات إدارية')
    
    return render(request, 'admin/simple_login.html')


def admin_logout_view(request):
    """تسجيل خروج الإدارة"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('company:admin_login')


@login_required
@user_passes_test(is_admin)
def admin_dashboard_view(request):
    """لوحة التحكم الرئيسية"""
    # إحصائيات عامة
    total_services = Service.objects.count()
    active_services = Service.objects.filter(is_active=True).count()
    total_requests = ServiceRequest.objects.count()
    pending_requests = ServiceRequest.objects.filter(status='pending').count()
    total_users = User.objects.filter(role='user').count()
    total_messages = ContactMessage.objects.count()
    unread_messages = ContactMessage.objects.filter(is_read=False).count()
    
    # إحصائيات الطلبات حسب الحالة
    request_stats = ServiceRequest.objects.values('status').annotate(count=Count('id'))
    
    # الطلبات الحديثة
    recent_requests = ServiceRequest.objects.select_related('service').order_by('-created_at')[:10]
    
    # الرسائل الحديثة
    recent_messages = ContactMessage.objects.order_by('-created_at')[:5]
    
    # إحصائيات الخدمات الأكثر طلباً
    popular_services = Service.objects.annotate(
        request_count=Count('requests')
    ).order_by('-request_count')[:5]
    
    # إحصائيات شهرية للطلبات
    current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    
    current_month_requests = ServiceRequest.objects.filter(created_at__gte=current_month).count()
    last_month_requests = ServiceRequest.objects.filter(
        created_at__gte=last_month,
        created_at__lt=current_month
    ).count()
    
    # حساب نسبة التغيير
    if last_month_requests > 0:
        requests_change = ((current_month_requests - last_month_requests) / last_month_requests) * 100
    else:
        requests_change = 100 if current_month_requests > 0 else 0
    
    context = {
        'total_services': total_services,
        'active_services': active_services,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'total_users': total_users,
        'total_messages': total_messages,
        'unread_messages': unread_messages,
        'request_stats': request_stats,
        'recent_requests': recent_requests,
        'recent_messages': recent_messages,
        'popular_services': popular_services,
        'current_month_requests': current_month_requests,
        'requests_change': requests_change,
        'current_time': timezone.now(),
    }
    
    return render(request, 'admin/simple_dashboard.html', context)


@login_required
@user_passes_test(is_admin)
def admin_services_view(request):
    """إدارة الخدمات"""
    services = Service.objects.all().order_by('-created_at')
    
    # فلترة الخدمات
    category = request.GET.get('category')
    status = request.GET.get('status')
    
    if category:
        services = services.filter(category=category)
    
    if status == 'active':
        services = services.filter(is_active=True)
    elif status == 'inactive':
        services = services.filter(is_active=False)
    elif status == 'featured':
        services = services.filter(is_featured=True)
    
    all_services = Service.objects.all()

    context = {
        'services': services,
        'categories': Service.SERVICE_CATEGORIES,
        'selected_category': category,
        'selected_status': status,
        'total_services': all_services.count(),
        'active_services': all_services.filter(is_active=True).count(),
        'featured_services': all_services.filter(is_featured=True).count(),
        'total_requests': sum(service.requests.count() for service in all_services),
    }

    return render(request, 'admin/services_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_service_edit_view(request, service_id):
    """تعديل خدمة"""
    service = get_object_or_404(Service, id=service_id)
    
    if request.method == 'POST':
        # تحديث بيانات الخدمة
        service.name_ar = request.POST.get('name_ar')
        service.name_en = request.POST.get('name_en')
        service.description_ar = request.POST.get('description_ar')
        service.description_en = request.POST.get('description_en')
        service.category = request.POST.get('category')
        service.price_range_min = request.POST.get('price_range_min')
        service.price_range_max = request.POST.get('price_range_max')
        service.duration_hours = request.POST.get('duration_hours')
        service.is_active = 'is_active' in request.POST
        service.is_featured = 'is_featured' in request.POST
        
        # تحديث الميزات والمتطلبات
        features = request.POST.get('features', '').split('\n')
        service.features = [f.strip() for f in features if f.strip()]
        
        requirements = request.POST.get('requirements', '').split('\n')
        service.requirements = [r.strip() for r in requirements if r.strip()]
        
        service.save()
        messages.success(request, 'تم تحديث الخدمة بنجاح')
        return redirect('company:admin_services')
    
    context = {
        'service': service,
        'categories': Service.SERVICE_CATEGORIES,
    }
    
    return render(request, 'admin/service_edit.html', context)


@login_required
@user_passes_test(is_admin)
def admin_requests_view(request):
    """إدارة طلبات الخدمات"""
    requests = ServiceRequest.objects.select_related('service').order_by('-created_at')
    
    # فلترة الطلبات
    status = request.GET.get('status')
    priority = request.GET.get('priority')
    service_id = request.GET.get('service')
    
    if status:
        requests = requests.filter(status=status)
    
    if priority:
        requests = requests.filter(priority=priority)
    
    if service_id:
        requests = requests.filter(service_id=service_id)
    
    context = {
        'requests': requests,
        'status_choices': ServiceRequest.STATUS_CHOICES,
        'priority_choices': ServiceRequest.PRIORITY_CHOICES,
        'services': Service.objects.filter(is_active=True),
        'selected_status': status,
        'selected_priority': priority,
        'selected_service': service_id,
    }
    
    return render(request, 'admin/requests_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_request_detail_view(request, request_id):
    """تفاصيل طلب الخدمة"""
    service_request = get_object_or_404(ServiceRequest, id=request_id)
    
    if request.method == 'POST':
        # تحديث حالة الطلب
        service_request.status = request.POST.get('status')
        service_request.priority = request.POST.get('priority')
        service_request.estimated_cost = request.POST.get('estimated_cost') or None
        service_request.final_cost = request.POST.get('final_cost') or None
        service_request.admin_notes = request.POST.get('admin_notes')
        
        if request.POST.get('assigned_to'):
            service_request.assigned_to_id = request.POST.get('assigned_to')
        
        if service_request.status == 'completed' and not service_request.completed_at:
            service_request.completed_at = timezone.now()
        
        service_request.save()
        messages.success(request, 'تم تحديث الطلب بنجاح')
        return redirect('company:admin_request_detail', request_id=request_id)
    
    # المستخدمين الإداريين لتعيين الطلب
    admin_users = User.objects.filter(Q(is_staff=True) | Q(role='admin'))
    
    context = {
        'request': service_request,
        'status_choices': ServiceRequest.STATUS_CHOICES,
        'priority_choices': ServiceRequest.PRIORITY_CHOICES,
        'admin_users': admin_users,
    }
    
    return render(request, 'admin/request_detail.html', context)


@login_required
@user_passes_test(is_admin)
def admin_messages_view(request):
    """إدارة رسائل التواصل"""
    messages_list = ContactMessage.objects.order_by('-created_at')
    
    # فلترة الرسائل
    message_type = request.GET.get('type')
    status = request.GET.get('status')
    
    if message_type:
        messages_list = messages_list.filter(message_type=message_type)
    
    if status == 'unread':
        messages_list = messages_list.filter(is_read=False)
    elif status == 'read':
        messages_list = messages_list.filter(is_read=True)
    elif status == 'replied':
        messages_list = messages_list.filter(is_replied=True)
    
    context = {
        'messages': messages_list,
        'message_types': ContactMessage.MESSAGE_TYPES,
        'selected_type': message_type,
        'selected_status': status,
    }
    
    return render(request, 'admin/messages_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_settings_view(request):
    """إدارة إعدادات الموقع"""
    settings = SiteSettings.get_settings()

    if request.method == 'POST':
        # تحديث الإعدادات
        for field in SiteSettings._meta.fields:
            if field.name in request.POST and field.name not in ['id', 'created_at', 'updated_at']:
                value = request.POST.get(field.name)
                if field.name == 'show_stats':
                    value = 'show_stats' in request.POST
                elif field.get_internal_type() == 'IntegerField':
                    try:
                        value = int(value) if value else 0
                    except ValueError:
                        value = 0
                setattr(settings, field.name, value)

        settings.save()
        messages.success(request, 'تم حفظ الإعدادات بنجاح')
        return redirect('company:admin_settings')

    context = {
        'settings': settings,
    }

    return render(request, 'admin/settings_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_portfolio_view(request):
    """إدارة معرض الأعمال"""
    projects = Portfolio.objects.all().select_related('service').prefetch_related('images').order_by('-created_at')

    # إحصائيات المعرض
    total_projects = projects.count()
    published_projects = projects.filter(is_published=True).count()
    featured_projects = projects.filter(is_featured=True).count()
    draft_projects = projects.filter(is_published=False).count()

    # إحصائيات حسب النوع
    video_projects = projects.filter(media_type='video').count()
    gallery_projects = projects.filter(media_type='gallery').count()
    image_projects = projects.filter(media_type='image').count()

    # إحصائيات حسب الفئة
    category_stats = {}
    for category_code, category_name in Portfolio.PROJECT_CATEGORIES:
        category_stats[category_name] = projects.filter(category=category_code).count()

    context = {
        'projects': projects,
        'total_projects': total_projects,
        'published_projects': published_projects,
        'featured_projects': featured_projects,
        'draft_projects': draft_projects,
        'video_projects': video_projects,
        'gallery_projects': gallery_projects,
        'image_projects': image_projects,
        'category_stats': category_stats,
    }

    return render(request, 'admin/portfolio_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_testimonials_view(request):
    """إدارة آراء العملاء"""
    testimonials = Testimonial.objects.all().select_related('portfolio', 'service').order_by('-created_at')

    # إحصائيات آراء العملاء
    total_testimonials = testimonials.count()
    published_testimonials = testimonials.filter(is_published=True).count()
    featured_testimonials = testimonials.filter(is_featured=True).count()

    # إحصائيات التقييمات
    rating_stats = {}
    for i in range(1, 6):
        rating_stats[f'{i}_stars'] = testimonials.filter(rating=i).count()

    context = {
        'testimonials': testimonials,
        'total_testimonials': total_testimonials,
        'published_testimonials': published_testimonials,
        'featured_testimonials': featured_testimonials,
        'rating_stats': rating_stats,
    }

    return render(request, 'admin/testimonials_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_content_view(request):
    """إدارة المحتوى العام (FAQ, Blog, Team, Partners)"""

    # الأسئلة الشائعة
    faqs = FAQ.objects.all().order_by('display_order', '-created_at')
    total_faqs = faqs.count()
    published_faqs = faqs.filter(is_published=True).count()

    # المقالات
    blog_posts = BlogPost.objects.all().select_related('author').order_by('-created_at')
    total_posts = blog_posts.count()
    published_posts = blog_posts.filter(is_published=True).count()
    featured_posts = blog_posts.filter(is_featured=True).count()

    # فريق العمل
    team_members = Team.objects.all().order_by('display_order', 'name_ar')
    total_team = team_members.count()
    active_team = team_members.filter(is_active=True).count()

    # الشركاء
    partners = Partner.objects.all().order_by('display_order', 'name_ar')
    total_partners = partners.count()
    active_partners = partners.filter(is_active=True).count()
    homepage_partners = partners.filter(show_on_homepage=True).count()

    context = {
        'faqs': faqs,
        'total_faqs': total_faqs,
        'published_faqs': published_faqs,
        'blog_posts': blog_posts,
        'total_posts': total_posts,
        'published_posts': published_posts,
        'featured_posts': featured_posts,
        'team_members': team_members,
        'total_team': total_team,
        'active_team': active_team,
        'partners': partners,
        'total_partners': total_partners,
        'active_partners': active_partners,
        'homepage_partners': homepage_partners,
    }

    return render(request, 'admin/content_dark.html', context)


# ==================== CRUD Operations for Portfolio ====================

@login_required
@user_passes_test(is_admin)
def admin_portfolio_add_view(request):
    """إضافة مشروع جديد"""
    if request.method == 'POST':
        try:
            # إنشاء مشروع جديد
            portfolio = Portfolio.objects.create(
                title_ar=request.POST.get('title_ar'),
                title_en=request.POST.get('title_en', ''),
                description_ar=request.POST.get('description_ar'),
                description_en=request.POST.get('description_en', ''),
                category=request.POST.get('category'),
                service_id=request.POST.get('service') if request.POST.get('service') else None,
                client_name=request.POST.get('client_name', ''),
                location=request.POST.get('location', ''),
                duration=request.POST.get('duration', ''),
                media_type=request.POST.get('media_type'),
                video_url=request.POST.get('video_url', ''),
                is_featured='is_featured' in request.POST,
                is_published='is_published' in request.POST,
                show_on_homepage='show_on_homepage' in request.POST,
                display_order=int(request.POST.get('display_order', 0)),
                technologies_used=request.POST.get('technologies_used', ''),
                project_cost=request.POST.get('project_cost', ''),
                client_feedback=request.POST.get('client_feedback', '')
            )

            # رفع الصورة الرئيسية
            if 'featured_image' in request.FILES:
                portfolio.featured_image = request.FILES['featured_image']
                portfolio.save()

            # رفع ملف الفيديو
            if 'video_file' in request.FILES:
                portfolio.video_file = request.FILES['video_file']
                portfolio.save()

            messages.success(request, f'تم إضافة المشروع "{portfolio.title_ar}" بنجاح', extra_tags='admin_portfolio')
            return redirect('company:admin_portfolio')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة المشروع: {str(e)}')

    # جلب البيانات المطلوبة للنموذج
    services = Service.objects.filter(is_active=True)

    context = {
        'services': services,
        'media_types': Portfolio.MEDIA_TYPES,
        'categories': Portfolio.PROJECT_CATEGORIES,
    }

    return render(request, 'admin/portfolio_add_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_portfolio_edit_view(request, portfolio_id):
    """تعديل مشروع موجود"""
    portfolio = get_object_or_404(Portfolio, id=portfolio_id)

    if request.method == 'POST':
        try:
            # تحديث بيانات المشروع
            portfolio.title_ar = request.POST.get('title_ar')
            portfolio.title_en = request.POST.get('title_en', '')
            portfolio.description_ar = request.POST.get('description_ar')
            portfolio.description_en = request.POST.get('description_en', '')
            portfolio.category = request.POST.get('category')
            portfolio.service_id = request.POST.get('service') if request.POST.get('service') else None
            portfolio.client_name = request.POST.get('client_name', '')
            portfolio.location = request.POST.get('location', '')
            portfolio.duration = request.POST.get('duration', '')
            portfolio.media_type = request.POST.get('media_type')
            portfolio.video_url = request.POST.get('video_url', '')
            portfolio.is_featured = 'is_featured' in request.POST
            portfolio.is_published = 'is_published' in request.POST
            portfolio.show_on_homepage = 'show_on_homepage' in request.POST
            portfolio.display_order = int(request.POST.get('display_order', 0))
            portfolio.technologies_used = request.POST.get('technologies_used', '')
            portfolio.project_cost = request.POST.get('project_cost', '')
            portfolio.client_feedback = request.POST.get('client_feedback', '')

            # تحديث الصورة الرئيسية إذا تم رفع صورة جديدة
            if 'featured_image' in request.FILES:
                portfolio.featured_image = request.FILES['featured_image']

            # تحديث ملف الفيديو إذا تم رفع ملف جديد
            if 'video_file' in request.FILES:
                portfolio.video_file = request.FILES['video_file']

            portfolio.save()

            messages.success(request, f'تم تحديث المشروع "{portfolio.title_ar}" بنجاح', extra_tags='admin_portfolio')
            return redirect('company:admin_portfolio')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث المشروع: {str(e)}')

    # جلب البيانات المطلوبة للنموذج
    services = Service.objects.filter(is_active=True)

    context = {
        'portfolio': portfolio,
        'services': services,
        'media_types': Portfolio.MEDIA_TYPES,
        'categories': Portfolio.PROJECT_CATEGORIES,
    }

    return render(request, 'admin/portfolio_edit_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_portfolio_delete_view(request, portfolio_id):
    """حذف مشروع"""
    portfolio = get_object_or_404(Portfolio, id=portfolio_id)

    if request.method == 'POST':
        try:
            portfolio_title = portfolio.title_ar
            portfolio.delete()
            messages.success(request, f'تم حذف المشروع "{portfolio_title}" بنجاح', extra_tags='admin_portfolio')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف المشروع: {str(e)}')

    return redirect('company:admin_portfolio')


@login_required
@user_passes_test(is_admin)
def admin_portfolio_toggle_status_view(request, portfolio_id):
    """تغيير حالة النشر للمشروع"""
    portfolio = get_object_or_404(Portfolio, id=portfolio_id)

    if request.method == 'POST':
        try:
            portfolio.is_published = not portfolio.is_published
            portfolio.save()

            status = "نشر" if portfolio.is_published else "إلغاء نشر"
            messages.success(request, f'تم {status} المشروع "{portfolio.title_ar}" بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تغيير حالة المشروع: {str(e)}')

    return redirect('company:admin_portfolio')


# ==================== CRUD Operations for Testimonials ====================

@login_required
@user_passes_test(is_admin)
def admin_testimonial_add_view(request):
    """إضافة رأي عميل جديد"""
    if request.method == 'POST':
        try:
            testimonial = Testimonial.objects.create(
                client_name=request.POST.get('client_name'),
                client_position=request.POST.get('client_position', ''),
                client_company=request.POST.get('client_company', ''),
                testimonial_ar=request.POST.get('testimonial_ar'),
                testimonial_en=request.POST.get('testimonial_en', ''),
                rating=int(request.POST.get('rating', 5)),
                portfolio_id=request.POST.get('portfolio') if request.POST.get('portfolio') else None,
                service_id=request.POST.get('service') if request.POST.get('service') else None,
                is_featured='is_featured' in request.POST,
                is_published='is_published' in request.POST,
                show_on_homepage='show_on_homepage' in request.POST
            )

            # رفع صورة العميل
            if 'client_image' in request.FILES:
                testimonial.client_image = request.FILES['client_image']
                testimonial.save()

            messages.success(request, f'تم إضافة رأي العميل "{testimonial.client_name}" بنجاح')
            return redirect('company:admin_testimonials')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة رأي العميل: {str(e)}')

    # جلب البيانات المطلوبة للنموذج
    portfolios = Portfolio.objects.filter(is_published=True)
    services = Service.objects.filter(is_active=True)

    context = {
        'portfolios': portfolios,
        'services': services,
        'rating_choices': range(1, 6),
    }

    return render(request, 'admin/testimonial_add_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_testimonial_edit_view(request, testimonial_id):
    """تعديل رأي عميل موجود"""
    testimonial = get_object_or_404(Testimonial, id=testimonial_id)

    if request.method == 'POST':
        try:
            testimonial.client_name = request.POST.get('client_name')
            testimonial.client_position = request.POST.get('client_position', '')
            testimonial.client_company = request.POST.get('client_company', '')
            testimonial.testimonial_ar = request.POST.get('testimonial_ar')
            testimonial.testimonial_en = request.POST.get('testimonial_en', '')
            testimonial.rating = int(request.POST.get('rating', 5))
            testimonial.portfolio_id = request.POST.get('portfolio') if request.POST.get('portfolio') else None
            testimonial.service_id = request.POST.get('service') if request.POST.get('service') else None
            testimonial.is_featured = 'is_featured' in request.POST
            testimonial.is_published = 'is_published' in request.POST
            testimonial.show_on_homepage = 'show_on_homepage' in request.POST

            # تحديث صورة العميل إذا تم رفع صورة جديدة
            if 'client_image' in request.FILES:
                testimonial.client_image = request.FILES['client_image']

            testimonial.save()

            messages.success(request, f'تم تحديث رأي العميل "{testimonial.client_name}" بنجاح')
            return redirect('company:admin_testimonials')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث رأي العميل: {str(e)}')

    # جلب البيانات المطلوبة للنموذج
    portfolios = Portfolio.objects.filter(is_published=True)
    services = Service.objects.filter(is_active=True)

    context = {
        'testimonial': testimonial,
        'portfolios': portfolios,
        'services': services,
        'rating_choices': range(1, 6),
    }

    return render(request, 'admin/testimonial_edit_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_testimonial_delete_view(request, testimonial_id):
    """حذف رأي عميل"""
    testimonial = get_object_or_404(Testimonial, id=testimonial_id)

    if request.method == 'POST':
        try:
            client_name = testimonial.client_name
            testimonial.delete()
            messages.success(request, f'تم حذف رأي العميل "{client_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف رأي العميل: {str(e)}')

    return redirect('company:admin_testimonials')


# ==================== CRUD Operations for Services ====================

@login_required
@user_passes_test(is_admin)
def admin_service_add_view(request):
    """إضافة خدمة جديدة"""
    if request.method == 'POST':
        try:
            service = Service.objects.create(
                name=request.POST.get('name'),
                description=request.POST.get('description'),
                price=request.POST.get('price', ''),
                duration=request.POST.get('duration', ''),
                features=request.POST.get('features', ''),
                is_featured='is_featured' in request.POST,
                is_active='is_active' in request.POST,
                display_order=int(request.POST.get('display_order', 0))
            )

            # رفع صورة الخدمة
            if 'image' in request.FILES:
                service.image = request.FILES['image']
                service.save()

            messages.success(request, f'تم إضافة الخدمة "{service.name}" بنجاح')
            return redirect('company:admin_services')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة الخدمة: {str(e)}')

    return render(request, 'admin/service_add_dark.html')


@login_required
@user_passes_test(is_admin)
def admin_service_edit_view(request, service_id):
    """تعديل خدمة موجودة"""
    service = get_object_or_404(Service, id=service_id)

    if request.method == 'POST':
        try:
            service.name = request.POST.get('name')
            service.description = request.POST.get('description')
            service.price = request.POST.get('price', '')
            service.duration = request.POST.get('duration', '')
            service.features = request.POST.get('features', '')
            service.is_featured = 'is_featured' in request.POST
            service.is_active = 'is_active' in request.POST
            service.display_order = int(request.POST.get('display_order', 0))

            # تحديث صورة الخدمة إذا تم رفع صورة جديدة
            if 'image' in request.FILES:
                service.image = request.FILES['image']

            service.save()

            messages.success(request, f'تم تحديث الخدمة "{service.name}" بنجاح')
            return redirect('company:admin_services')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث الخدمة: {str(e)}')

    context = {
        'service': service,
    }

    return render(request, 'admin/service_edit_dark.html', context)


@login_required
@user_passes_test(is_admin)
def admin_service_delete_view(request, service_id):
    """حذف خدمة"""
    service = get_object_or_404(Service, id=service_id)

    if request.method == 'POST':
        try:
            service_name = service.name
            service.delete()
            messages.success(request, f'تم حذف الخدمة "{service_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف الخدمة: {str(e)}')

    return redirect('company:admin_services')


@login_required
@user_passes_test(is_admin)
def admin_service_toggle_status_view(request, service_id):
    """تغيير حالة تفعيل الخدمة"""
    service = get_object_or_404(Service, id=service_id)

    if request.method == 'POST':
        try:
            service.is_active = not service.is_active
            service.save()

            status = "تفعيل" if service.is_active else "إلغاء تفعيل"
            messages.success(request, f'تم {status} الخدمة "{service.name}" بنجاح')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تغيير حالة الخدمة: {str(e)}')

    return redirect('company:admin_services')
