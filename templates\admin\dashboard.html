<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز التحكم - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Admin Futuristic CSS -->
    <link href="/static/css/admin-futuristic.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل المستقبلي -->
    <nav class="navbar navbar-expand-lg navbar-cyber fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand navbar-brand-cyber" href="#">
                <i class="fas fa-shield-alt me-2"></i>IQHome Control
            </a>
            
            <div class="dashboard-time">
                <i class="fas fa-clock me-2"></i>
                <span id="currentTime">{{ current_time|date:"H:i:s" }}</span>
            </div>
            
            <div class="d-flex align-items-center">
                <!-- الإشعارات -->
                <div class="dropdown me-3">
                    <button class="btn btn-neon dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        {% if unread_messages > 0 %}
                            <span class="badge bg-danger">{{ unread_messages }}</span>
                        {% endif %}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><h6 class="dropdown-header">الإشعارات الحديثة</h6></li>
                        {% for message in recent_messages|slice:":3" %}
                        <li>
                            <a class="dropdown-item" href="{% url 'company:admin_messages' %}">
                                <i class="fas fa-envelope me-2"></i>
                                {{ message.subject|truncatechars:30 }}
                                <small class="d-block text-muted">{{ message.created_at|timesince }}</small>
                            </a>
                        </li>
                        {% endfor %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'company:admin_messages' %}">عرض جميع الرسائل</a></li>
                    </ul>
                </div>
                
                <!-- ملف المستخدم -->
                <div class="dropdown">
                    <button class="btn btn-cyber dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-shield me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="{% url 'company:admin_settings' %}"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'company:admin_logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar-cyber">
                <ul class="sidebar-menu">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}" class="active">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                            {% if pending_requests > 0 %}
                                <span class="badge bg-warning text-dark ms-auto">{{ pending_requests }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                            {% if unread_messages > 0 %}
                                <span class="badge bg-danger ms-auto">{{ unread_messages }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_analytics' %}">
                            <i class="fas fa-chart-line"></i>
                            التحليلات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس لوحة التحكم -->
                <div class="dashboard-header fade-in-cyber">
                    <h1 class="dashboard-title">مرحباً بك في مركز التحكم</h1>
                    <p class="dashboard-subtitle">إدارة شاملة لجميع عمليات IQHome</p>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions fade-in-cyber">
                    <a href="{% url 'company:admin_services' %}" class="quick-action-btn">
                        <i class="fas fa-plus"></i>
                        إضافة خدمة جديدة
                    </a>
                    <a href="{% url 'company:admin_requests' %}?status=pending" class="quick-action-btn">
                        <i class="fas fa-clock"></i>
                        الطلبات المعلقة
                    </a>
                    <a href="{% url 'company:admin_messages' %}?status=unread" class="quick-action-btn">
                        <i class="fas fa-envelope-open"></i>
                        الرسائل غير المقروءة
                    </a>
                    <a href="{% url 'company:admin_analytics' %}" class="quick-action-btn">
                        <i class="fas fa-chart-bar"></i>
                        تقارير مفصلة
                    </a>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="advanced-stat-card scroll-reveal-cyber">
                            <i class="fas fa-cogs stat-icon-advanced text-cyber"></i>
                            <div class="stat-value-advanced">{{ total_services }}</div>
                            <div class="stat-label-advanced">إجمالي الخدمات</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                {{ active_services }} نشطة
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="advanced-stat-card scroll-reveal-cyber">
                            <i class="fas fa-clipboard-list stat-icon-advanced text-neon"></i>
                            <div class="stat-value-advanced">{{ total_requests }}</div>
                            <div class="stat-label-advanced">طلبات الخدمات</div>
                            <div class="stat-change {% if requests_change >= 0 %}positive{% else %}negative{% endif %}">
                                <i class="fas fa-arrow-{% if requests_change >= 0 %}up{% else %}down{% endif %}"></i>
                                {{ requests_change|floatformat:1 }}% هذا الشهر
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="advanced-stat-card scroll-reveal-cyber">
                            <i class="fas fa-users stat-icon-advanced text-plasma"></i>
                            <div class="stat-value-advanced">{{ total_users }}</div>
                            <div class="stat-label-advanced">العملاء المسجلين</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                نمو مستمر
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="advanced-stat-card scroll-reveal-cyber">
                            <i class="fas fa-envelope stat-icon-advanced text-quantum"></i>
                            <div class="stat-value-advanced">{{ total_messages }}</div>
                            <div class="stat-label-advanced">رسائل التواصل</div>
                            <div class="stat-change {% if unread_messages > 0 %}negative{% else %}positive{% endif %}">
                                <i class="fas fa-envelope-open"></i>
                                {{ unread_messages }} غير مقروءة
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- الرسم البياني للطلبات -->
                    <div class="col-lg-8">
                        <div class="chart-container scroll-reveal-cyber">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line me-2"></i>
                                إحصائيات الطلبات حسب الحالة
                            </h3>
                            <canvas id="requestsChart" height="300"></canvas>
                        </div>
                    </div>

                    <!-- الأنشطة الحديثة -->
                    <div class="col-lg-4">
                        <div class="activity-list scroll-reveal-cyber">
                            <div class="p-3 border-bottom border-cyber">
                                <h5 class="text-cyber mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    الأنشطة الحديثة
                                </h5>
                            </div>
                            {% for request in recent_requests|slice:":5" %}
                            <div class="activity-item d-flex">
                                <div class="activity-icon">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">طلب خدمة جديد</div>
                                    <div class="activity-description">
                                        {{ request.customer_name }} - {{ request.service.name_ar }}
                                    </div>
                                    <div class="activity-time">{{ request.created_at|timesince }}</div>
                                </div>
                            </div>
                            {% endfor %}
                            
                            {% for message in recent_messages|slice:":3" %}
                            <div class="activity-item d-flex">
                                <div class="activity-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">رسالة جديدة</div>
                                    <div class="activity-description">
                                        {{ message.name }} - {{ message.subject|truncatechars:30 }}
                                    </div>
                                    <div class="activity-time">{{ message.created_at|timesince }}</div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- الخدمات الأكثر طلباً -->
                <div class="row g-4 mt-4">
                    <div class="col-12">
                        <div class="card-cyber scroll-reveal-cyber">
                            <div class="card-body">
                                <h3 class="text-cyber mb-4">
                                    <i class="fas fa-star me-2"></i>
                                    الخدمات الأكثر طلباً
                                </h3>
                                <div class="row">
                                    {% for service in popular_services %}
                                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                        <div class="text-center">
                                            <div class="stat-number">{{ service.request_count }}</div>
                                            <div class="stat-label">{{ service.name_ar }}</div>
                                            <div class="progress-cyber mt-2">
                                                <div class="progress-bar-cyber" style="width: {% widthratio service.request_count popular_services.0.request_count 100 %}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- لوحة الإشعارات -->
    <div class="notification-panel" id="notificationPanel"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تحديث الوقت الحالي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-EG', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);
        
        // تأثيرات التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal-cyber').forEach(el => {
            observer.observe(el);
        });
        
        // الرسم البياني للطلبات
        const ctx = document.getElementById('requestsChart').getContext('2d');
        const requestsChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for stat in request_stats %}
                        '{{ stat.status }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for stat in request_stats %}
                            {{ stat.count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(255, 0, 110, 0.8)',
                        'rgba(16, 245, 74, 0.8)',
                        'rgba(255, 215, 0, 0.8)'
                    ],
                    borderColor: [
                        'rgba(0, 212, 255, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(255, 0, 110, 1)',
                        'rgba(16, 245, 74, 1)',
                        'rgba(255, 215, 0, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#00D4FF',
                            font: {
                                family: 'Cairo, sans-serif'
                            }
                        }
                    }
                }
            }
        });
        
        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification-item ${type}`;
            notification.innerHTML = `
                <button class="notification-close">&times;</button>
                <div class="notification-content">
                    <strong>${message}</strong>
                </div>
            `;
            
            document.getElementById('notificationPanel').appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }, 5000);
            
            // إزالة عند النقر على زر الإغلاق
            notification.querySelector('.notification-close').addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            });
        }
        
        // إشعار ترحيبي
        setTimeout(() => {
            showNotification('مرحباً بك في مركز التحكم المتطور!', 'success');
        }, 1000);
        
        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            // هنا يمكن إضافة كود لتحديث البيانات عبر AJAX
            console.log('تحديث البيانات...');
        }, 30000);
    </script>
</body>
</html>
