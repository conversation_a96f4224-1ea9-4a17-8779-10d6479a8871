# IQHome - Iraqi Smart Home Control Platform

🏠 **IQHome** is a comprehensive smart home control platform designed specifically for Iraqi households. It enables users to monitor and control smart devices in their homes including lighting, water systems, energy monitoring, security cameras, and more.

## 🌟 Features

### For Users
- **Device Management**: Add, configure, and control smart devices
- **Real-time Monitoring**: Monitor device status, energy usage, and sensor data
- **Mobile-Friendly Interface**: Responsive web design for all devices
- **Notifications**: Receive alerts about device status and system events
- **Support System**: Submit and track support tickets
- **Profile Management**: Manage account settings and preferences

### For Administrators
- **User Management**: Monitor and manage user accounts
- **Device Monitoring**: Track all devices across the platform
- **Support Management**: Handle user support requests
- **System Analytics**: View platform statistics and usage data
- **Notification Management**: Send system-wide notifications
- **System Logs**: Monitor platform activity and security events

## 🛠️ Technology Stack

- **Backend**: Django 5.2.4 + Django REST Framework
- **Database**: PostgreSQL (SQLite for development)
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: Swagger/OpenAPI
- **Frontend**: Bootstrap 5 + Custom CSS/JavaScript
- **Icons**: Font Awesome 6

## 📋 Requirements

- Python 3.8+
- Django 5.2.4
- PostgreSQL (for production)
- Virtual Environment (recommended)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd smart-home
```

### 2. Set Up Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Database Setup
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Create sample data (optional)
python manage.py create_sample_data --users 5 --devices 10
```

### 5. Run Development Server
```bash
python manage.py runserver 8001
```

### 6. Access the Platform
- **Home Page**: http://127.0.0.1:8001/
- **User Login**: http://127.0.0.1:8001/login/
- **Admin Login**: http://127.0.0.1:8001/admin/login/
- **API Documentation**: http://127.0.0.1:8001/swagger/
- **Django Admin**: http://127.0.0.1:8001/admin/

## 📚 API Documentation

The platform provides a comprehensive REST API documented with Swagger/OpenAPI.

### Authentication
```bash
# Get JWT token
POST /api/token/
{
    "username": "your_username",
    "password": "your_password"
}

# Use token in requests
Authorization: Bearer <your_jwt_token>
```

### Key Endpoints

#### Authentication
- `POST /api/token/` - Obtain JWT token
- `POST /api/token/refresh/` - Refresh JWT token
- `POST /api/register/` - User registration

#### Devices
- `GET /api/devices/` - List user devices
- `POST /api/devices/` - Add new device
- `GET /api/devices/{id}/` - Get device details
- `POST /api/devices/{id}/commands/` - Send device command

#### Support
- `GET /api/tickets/` - List support tickets
- `POST /api/tickets/` - Create support ticket
- `GET /api/tickets/{id}/` - Get ticket details

#### Notifications
- `GET /api/notifications/` - List notifications
- `POST /api/notifications/{id}/read/` - Mark as read

## 🏗️ Project Structure

```
smart-home/
├── config/                 # Django project settings
├── accounts/              # User management app
├── devices/               # Device management app
├── support/               # Support system app
├── notifications/         # Notification system app
├── templates/             # HTML templates
├── static/                # Static files (CSS, JS, images)
├── media/                 # User uploaded files
├── requirements.txt       # Python dependencies
└── manage.py             # Django management script
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the project root:

```env
SECRET_KEY=your-secret-key
DEBUG=True
DATABASE_URL=postgresql://user:password@localhost:5432/iqhome_db
```

### Database Configuration
For production, update `config/settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'iqhome_db',
        'USER': 'iqhome_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

## 🧪 Testing

Run the test suite:
```bash
python manage.py test
```

## 📱 Supported Device Types

- **Smart Lights**: Control brightness and on/off state
- **Smart Switches**: Toggle electrical devices
- **Sensors**: Temperature, humidity, motion sensors
- **Security Cameras**: Monitor and control cameras
- **Thermostats**: Climate control systems
- **Smart Locks**: Door and window locks
- **Smart Outlets**: Controllable power outlets
- **Water Controllers**: Water flow and monitoring
- **Energy Monitors**: Power consumption tracking

## 🔐 Security Features

- JWT-based authentication
- Role-based access control (Admin/User)
- CSRF protection
- SQL injection prevention
- XSS protection
- Secure password validation
- System activity logging

## 🌍 Localization

The platform is designed for Iraqi users with:
- Arabic language support (planned)
- Local time zone handling
- Iraqi phone number formats
- Regional preferences

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: http://127.0.0.1:8001/swagger/
- Issues: Create an issue in the repository

## 🚀 Deployment

### Production Deployment
1. Set up PostgreSQL database
2. Configure environment variables
3. Collect static files: `python manage.py collectstatic`
4. Run migrations: `python manage.py migrate`
5. Set up web server (nginx + gunicorn recommended)
6. Configure SSL certificate
7. Set up monitoring and logging

### Docker Deployment (Optional)
```bash
# Build and run with Docker
docker-compose up -d
```

---

**Made with ❤️ for Iraqi Smart Homes**
