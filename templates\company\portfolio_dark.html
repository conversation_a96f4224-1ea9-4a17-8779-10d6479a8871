<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معرض أعمالنا - {{ company_name }}</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container">
            <a class="navbar-brand-dark" href="{% url 'company:home' %}">
                <i class="fas fa-home me-2"></i>{{ company_name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link-dark" href="{% url 'company:home' %}">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link-dark" href="{% url 'company:services' %}">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link-dark active" href="{% url 'company:portfolio' %}">معرض أعمالنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link-dark" href="{% url 'company:contact' %}">تواصل معنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="{% url 'company:request_service' %}" class="btn-dark-pro">
                        <i class="fas fa-phone"></i>اطلب خدمة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس الصفحة -->
    <section class="hero-dark-pro section-with-navbar">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto hero-content-dark">
                    <h1 class="hero-title-dark">معرض أعمالنا</h1>
                    <p class="hero-subtitle-dark">
                        اكتشف مجموعة من أفضل مشاريعنا في مجال المنازل الذكية
                        <br>
                        مشاريع حقيقية لعملاء راضين عن خدماتنا المتميزة
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- فلترة المشاريع -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card-dark-pro">
                        <div class="d-flex flex-wrap gap-2 justify-content-center">
                            <button class="btn-dark-pro filter-btn active" data-filter="all">
                                <i class="fas fa-th"></i>جميع المشاريع
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="smart_home">
                                <i class="fas fa-home"></i>منازل ذكية
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="security">
                                <i class="fas fa-shield-alt"></i>أنظمة الأمان
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="lighting">
                                <i class="fas fa-lightbulb"></i>أنظمة الإضاءة
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="climate">
                                <i class="fas fa-thermometer-half"></i>التحكم في المناخ
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="entertainment">
                                <i class="fas fa-tv"></i>أنظمة الترفيه
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شبكة المشاريع -->
            <div class="row g-4" id="portfolioGrid">
                {% for project in projects %}
                <div class="col-lg-4 col-md-6 portfolio-item" data-category="{{ project.category }}">
                    <div class="card-dark-pro h-100 portfolio-card">
                        <div class="position-relative overflow-hidden" style="border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;">
                            {% if project.featured_image %}
                                {% if project.media_type == 'video' %}
                                    <div class="portfolio-video-container">
                                        {% if project.video_url %}
                                            <div class="video-thumbnail" style="background-image: url('{{ project.featured_image.url }}'); height: 250px; background-size: cover; background-position: center;">
                                                <div class="video-play-overlay">
                                                    <button class="btn-dark-pro video-play-btn" data-video="{{ project.video_url }}">
                                                        <i class="fas fa-play fa-2x"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        {% elif project.video_file %}
                                            <video controls style="width: 100%; height: 250px; object-fit: cover;">
                                                <source src="{{ project.video_file.url }}" type="video/mp4">
                                            </video>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <img src="{{ project.featured_image.url }}" alt="{{ project.title_ar }}" class="img-fluid" style="height: 250px; width: 100%; object-fit: cover;">
                                    {% if project.media_type == 'gallery' and project.images.count > 0 %}
                                        <div class="portfolio-overlay">
                                            <div class="portfolio-overlay-content">
                                                <a href="{{ project.featured_image.url }}" data-lightbox="gallery-{{ project.id }}" data-title="{{ project.title_ar }}" class="btn-outline-dark">
                                                    <i class="fas fa-search-plus"></i>
                                                </a>
                                                {% for image in project.images.all %}
                                                    <a href="{{ image.image.url }}" data-lightbox="gallery-{{ project.id }}" data-title="{{ image.caption_ar }}" style="display: none;"></a>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="portfolio-overlay">
                                            <div class="portfolio-overlay-content">
                                                <a href="{{ project.featured_image.url }}" data-lightbox="single-{{ project.id }}" data-title="{{ project.title_ar }}" class="btn-outline-dark">
                                                    <i class="fas fa-search-plus"></i>
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endif %}
                            {% else %}
                                <!-- صورة افتراضية في حالة عدم وجود صورة -->
                                <div class="d-flex align-items-center justify-content-center" style="height: 250px; background: linear-gradient(135deg, var(--charcoal-medium), var(--charcoal-dark)); border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-3x text-muted-dark mb-2"></i>
                                        <p class="text-muted-dark mb-0">لا توجد صورة</p>
                                    </div>
                                </div>
                            {% endif %}
                            
                            <!-- شارة نوع الوسائط -->
                            <div class="position-absolute top-0 start-0 m-3">
                                <span class="badge-primary">
                                    <i class="{{ project.get_media_type_display_icon }}"></i>
                                    {{ project.get_media_type_display }}
                                </span>
                            </div>
                            
                            {% if project.is_featured %}
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge-warning">
                                        <i class="fas fa-star"></i>مميز
                                    </span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="p-4">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="text-primary-dark mb-0">{{ project.title_ar }}</h5>
                                <span class="badge-secondary">{{ project.get_category_display }}</span>
                            </div>
                            
                            <p class="text-secondary-dark mb-3">{{ project.description_ar|truncatewords:20 }}</p>
                            
                            <div class="row g-2 mb-3">
                                {% if project.client_name %}
                                    <div class="col-6">
                                        <small class="text-muted-dark">
                                            <i class="fas fa-user me-1"></i>{{ project.client_name }}
                                        </small>
                                    </div>
                                {% endif %}
                                {% if project.location %}
                                    <div class="col-6">
                                        <small class="text-muted-dark">
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ project.location }}
                                        </small>
                                    </div>
                                {% endif %}
                                {% if project.project_date %}
                                    <div class="col-6">
                                        <small class="text-muted-dark">
                                            <i class="fas fa-calendar me-1"></i>{{ project.project_date|date:"Y" }}
                                        </small>
                                    </div>
                                {% endif %}
                                {% if project.duration %}
                                    <div class="col-6">
                                        <small class="text-muted-dark">
                                            <i class="fas fa-clock me-1"></i>{{ project.duration }}
                                        </small>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button class="btn-outline-dark flex-fill" onclick="showProjectDetails('{{ project.id }}')">
                                    <i class="fas fa-info-circle"></i>التفاصيل
                                </button>
                                {% if project.service %}
                                    <a href="{% url 'company:request_service' %}?service={{ project.service.id }}" class="btn-dark-pro flex-fill">
                                        <i class="fas fa-phone"></i>اطلب مثله
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="card-dark-pro text-center">
                        <div class="py-5">
                            <i class="fas fa-folder-open fa-3x text-muted-dark mb-3"></i>
                            <h4 class="text-secondary-dark">لا توجد مشاريع متاحة حالياً</h4>
                            <p class="text-muted-dark">سيتم إضافة مشاريعنا قريباً</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- إحصائيات المشاريع -->
            {% if projects %}
            <div class="row g-4 mt-5">
                <div class="col-12">
                    <div class="card-dark-pro">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark text-center">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات مشاريعنا
                            </h3>
                        </div>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ total_projects }}</div>
                                    <div class="stat-label-dark">إجمالي المشاريع</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ featured_projects }}</div>
                                    <div class="stat-label-dark">مشاريع مميزة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ video_projects }}</div>
                                    <div class="stat-label-dark">مقاطع فيديو</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ gallery_projects }}</div>
                                    <div class="stat-label-dark">معارض صور</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </section>

    <!-- Modal تفاصيل المشروع -->
    <div class="modal fade" id="projectDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: var(--charcoal-medium); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title text-primary-dark">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل المشروع
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body" id="projectDetailsContent">
                    <!-- سيتم تحميل تفاصيل المشروع هنا -->
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn-secondary-dark" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn-dark-pro" onclick="requestSimilarProject()">
                        <i class="fas fa-phone"></i>اطلب مشروع مماثل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal مشغل الفيديو -->
    <div class="modal fade" id="videoModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: var(--charcoal-dark); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title text-primary-dark">
                        <i class="fas fa-video me-2"></i>مشاهدة الفيديو
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body p-0">
                    <div id="videoContainer" style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
                        <!-- سيتم تحميل الفيديو هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التذييل الاحترافي -->
    <footer class="footer-dark-pro">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-primary-dark">{{ company_name }}</h4>
                    <p class="text-secondary-dark">{{ company_slogan }}</p>
                </div>
                <div class="col-md-6">
                    <h5 class="text-primary-dark">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'company:home' %}" class="text-secondary-dark text-decoration-none">الرئيسية</a></li>
                        <li><a href="{% url 'company:services' %}" class="text-secondary-dark text-decoration-none">خدماتنا</a></li>
                        <li><a href="{% url 'company:contact' %}" class="text-secondary-dark text-decoration-none">تواصل معنا</a></li>
                    </ul>
                </div>
            </div>
            <hr style="border-color: var(--border-color);" class="my-4">
            <p class="text-center text-muted-dark">&copy; 2025 {{ company_name }}. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Lightbox JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>

    <!-- JavaScript خاص بمعرض الأعمال -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إعداد Lightbox
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true,
                'albumLabel': 'صورة %1 من %2',
                'fadeDuration': 300,
                'imageFadeDuration': 300
            });

            // فلترة المشاريع
            const filterButtons = document.querySelectorAll('.filter-btn');
            const portfolioItems = document.querySelectorAll('.portfolio-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;

                    // تحديث الأزرار النشطة
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('btn-dark-pro');
                        btn.classList.add('btn-outline-dark');
                    });

                    this.classList.add('active');
                    this.classList.remove('btn-outline-dark');
                    this.classList.add('btn-dark-pro');

                    // فلترة المشاريع
                    portfolioItems.forEach(item => {
                        if (filter === 'all' || item.dataset.category === filter) {
                            item.style.display = 'block';
                            item.style.opacity = '0';
                            setTimeout(() => {
                                item.style.opacity = '1';
                            }, 100);
                        } else {
                            item.style.opacity = '0';
                            setTimeout(() => {
                                item.style.display = 'none';
                            }, 300);
                        }
                    });
                });
            });

            // مشغل الفيديو
            const videoPlayButtons = document.querySelectorAll('.video-play-btn');
            videoPlayButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const videoUrl = this.dataset.video;
                    playVideo(videoUrl);
                });
            });
        });

        function playVideo(videoUrl) {
            const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
            const videoContainer = document.getElementById('videoContainer');

            let embedUrl = '';
            if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
                const videoId = extractYouTubeId(videoUrl);
                embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
            } else if (videoUrl.includes('vimeo.com')) {
                const videoId = extractVimeoId(videoUrl);
                embedUrl = `https://player.vimeo.com/video/${videoId}?autoplay=1`;
            }

            if (embedUrl) {
                videoContainer.innerHTML = `
                    <iframe
                        src="${embedUrl}"
                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
                        frameborder="0"
                        allow="autoplay; fullscreen"
                        allowfullscreen>
                    </iframe>
                `;
                videoModal.show();
            }

            // تنظيف الفيديو عند إغلاق النافذة
            document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
                videoContainer.innerHTML = '';
            });
        }

        function extractYouTubeId(url) {
            const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
            const match = url.match(regExp);
            return (match && match[2].length === 11) ? match[2] : null;
        }

        function extractVimeoId(url) {
            const regExp = /vimeo.com\/(\d+)/;
            const match = url.match(regExp);
            return match ? match[1] : null;
        }

        function showProjectDetails(projectId) {
            // TODO: تحميل تفاصيل المشروع عبر AJAX
            const modal = new bootstrap.Modal(document.getElementById('projectDetailsModal'));
            const content = document.getElementById('projectDetailsContent');

            content.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-accent mb-3"></i>
                    <p class="text-secondary-dark">جاري تحميل تفاصيل المشروع...</p>
                </div>
            `;

            modal.show();

            // محاكاة تحميل البيانات
            setTimeout(() => {
                content.innerHTML = `
                    <div class="alert-dark alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تنفيذ عرض تفاصيل المشروع قريباً
                    </div>
                `;
            }, 1500);
        }

        function requestSimilarProject() {
            // إعادة توجيه لصفحة طلب الخدمة
            window.location.href = '{% url "company:request_service" %}';
        }

        // تأثيرات إضافية للبطاقات
        const portfolioCards = document.querySelectorAll('.portfolio-card');
        portfolioCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
