# Generated by Django 5.2.4 on 2025-07-29 02:14

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('device_type', models.CharField(choices=[('light', 'Smart Light'), ('switch', 'Smart Switch'), ('sensor', 'Sensor'), ('camera', 'Security Camera'), ('thermostat', 'Thermostat'), ('lock', 'Smart Lock'), ('outlet', 'Smart Outlet'), ('water', 'Water Controller'), ('energy', 'Energy Monitor'), ('other', 'Other Device')], max_length=20)),
                ('mac_address', models.CharField(max_length=17, unique=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('error', 'Error'), ('maintenance', 'Maintenance')], default='offline', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('location', models.CharField(blank=True, max_length=100, null=True)),
                ('model_number', models.CharField(blank=True, max_length=50, null=True)),
                ('firmware_version', models.CharField(blank=True, max_length=20, null=True)),
                ('manufacturer', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='devices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Device',
                'verbose_name_plural': 'Devices',
                'db_table': 'devices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ControlCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command_type', models.CharField(choices=[('turn_on', 'Turn On'), ('turn_off', 'Turn Off'), ('set_brightness', 'Set Brightness'), ('set_temperature', 'Set Temperature'), ('lock', 'Lock'), ('unlock', 'Unlock'), ('open', 'Open'), ('close', 'Close'), ('reset', 'Reset'), ('update_firmware', 'Update Firmware'), ('custom', 'Custom Command')], max_length=20)),
                ('command_data', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('acknowledged', 'Acknowledged'), ('completed', 'Completed'), ('failed', 'Failed'), ('timeout', 'Timeout')], default='pending', max_length=20)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('response_data', models.JSONField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('issued_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issued_commands', to=settings.AUTH_USER_MODEL)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commands', to='devices.device')),
            ],
            options={
                'verbose_name': 'Control Command',
                'verbose_name_plural': 'Control Commands',
                'db_table': 'control_commands',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='DeviceData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('key', models.CharField(max_length=50)),
                ('value', models.TextField()),
                ('unit', models.CharField(blank=True, max_length=20, null=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='data_points', to='devices.device')),
            ],
            options={
                'verbose_name': 'Device Data',
                'verbose_name_plural': 'Device Data',
                'db_table': 'device_data',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['device', 'key', '-timestamp'], name='device_data_device__1d689e_idx'), models.Index(fields=['timestamp'], name='device_data_timesta_fb415f_idx')],
            },
        ),
    ]
