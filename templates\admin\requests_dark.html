<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>IQHome - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}" class="active">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-clipboard-list me-2"></i>إدارة طلبات الخدمات
                            </h1>
                            <p class="text-secondary-dark">متابعة وإدارة جميع طلبات العملاء</p>
                        </div>
                        <div>
                            <button class="btn-outline-dark" onclick="exportRequests()">
                                <i class="fas fa-download"></i>تصدير البيانات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_requests }}</div>
                            <div class="stat-label-dark">إجمالي الطلبات</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number-dark">{{ pending_requests }}</div>
                            <div class="stat-label-dark">طلبات معلقة</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number-dark">{{ completed_requests }}</div>
                            <div class="stat-label-dark">طلبات مكتملة</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-number-dark">{{ today_requests }}</div>
                            <div class="stat-label-dark">طلبات اليوم</div>
                        </div>
                    </div>
                </div>

                <!-- فلترة وبحث -->
                <div class="card-dark-pro mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="form-group-dark">
                                <label class="form-label-dark">البحث في الطلبات</label>
                                <input type="text" class="form-control-dark" id="searchRequests" placeholder="ابحث بالاسم أو رقم الطلب...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">الحالة</label>
                                <select class="form-control-dark" id="filterStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">معلق</option>
                                    <option value="in_progress">قيد التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">الخدمة</label>
                                <select class="form-control-dark" id="filterService">
                                    <option value="">جميع الخدمات</option>
                                    {% for service in services %}
                                        <option value="{{ service.id }}">{{ service.name_ar }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">التاريخ</label>
                                <input type="date" class="form-control-dark" id="filterDate">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">المدينة</label>
                                <select class="form-control-dark" id="filterCity">
                                    <option value="">جميع المدن</option>
                                    {% for city in cities %}
                                        <option value="{{ city }}">{{ city }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group-dark">
                                <label class="form-label-dark">&nbsp;</label>
                                <button class="btn-outline-dark w-100" onclick="clearFilters()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الطلبات -->
                <div class="card-dark-pro">
                    <div class="card-header-dark">
                        <h3 class="card-title-dark">
                            <i class="fas fa-list me-2"></i>قائمة الطلبات
                        </h3>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table-dark-pro" id="requestsTable">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>الخدمة</th>
                                    <th>المدينة</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in requests %}
                                <tr data-status="{{ request.status }}" data-service="{{ request.service.id }}" data-city="{{ request.customer_city }}" data-date="{{ request.created_at|date:'Y-m-d' }}">
                                    <td>
                                        <a href="{% url 'company:admin_request_detail' request.id %}" class="text-accent text-decoration-none">
                                            <strong>{{ request.request_number }}</strong>
                                        </a>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="text-primary-dark mb-1">{{ request.customer_name }}</h6>
                                            <small class="text-secondary-dark">
                                                <i class="fas fa-phone me-1"></i>{{ request.customer_phone }}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="service-icon-dark me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                {% if request.service.category == 'installation' %}
                                                    <i class="fas fa-home"></i>
                                                {% elif request.service.category == 'maintenance' %}
                                                    <i class="fas fa-tools"></i>
                                                {% elif request.service.category == 'consultation' %}
                                                    <i class="fas fa-user-tie"></i>
                                                {% else %}
                                                    <i class="fas fa-cogs"></i>
                                                {% endif %}
                                            </div>
                                            <span class="text-secondary-dark">{{ request.service.name_ar }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-secondary-dark">{{ request.customer_city }}</span>
                                    </td>
                                    <td>
                                        {% if request.status == 'pending' %}
                                            <span class="badge-warning">{{ request.get_status_display }}</span>
                                        {% elif request.status == 'in_progress' %}
                                            <span class="badge-primary">{{ request.get_status_display }}</span>
                                        {% elif request.status == 'completed' %}
                                            <span class="badge-success">{{ request.get_status_display }}</span>
                                        {% elif request.status == 'cancelled' %}
                                            <span class="badge-danger">{{ request.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <small class="text-secondary-dark">{{ request.created_at|date:"d/m/Y" }}</small>
                                            <br>
                                            <small class="text-muted-dark">{{ request.created_at|time:"H:i" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{% url 'company:admin_request_detail' request.id %}" class="btn-outline-dark btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <div class="dropdown">
                                                <button class="btn-outline-dark btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" title="تغيير الحالة">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-dark">
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ request.id }}', 'pending')">
                                                        <i class="fas fa-clock me-2"></i>معلق
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ request.id }}', 'in_progress')">
                                                        <i class="fas fa-play me-2"></i>قيد التنفيذ
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ request.id }}', 'completed')">
                                                        <i class="fas fa-check me-2"></i>مكتمل
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updateStatus('{{ request.id }}', 'cancelled')">
                                                        <i class="fas fa-times me-2"></i>ملغي
                                                    </a></li>
                                                </ul>
                                            </div>
                                            <button class="btn-outline-dark btn-sm" onclick="contactCustomer('{{ request.customer_phone }}')" title="اتصال">
                                                <i class="fas fa-phone"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted-dark py-4">
                                        <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                                        <br>لا توجد طلبات بعد
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if requests.has_other_pages %}
                    <div class="d-flex justify-content-center mt-4">
                        <nav>
                            <ul class="pagination">
                                {% if requests.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link bg-dark text-light border-secondary" href="?page={{ requests.previous_page_number }}">السابق</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in requests.paginator.page_range %}
                                    {% if requests.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link bg-primary border-primary">{{ num }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link bg-dark text-light border-secondary" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if requests.has_next %}
                                    <li class="page-item">
                                        <a class="page-link bg-dark text-light border-secondary" href="?page={{ requests.next_page_number }}">التالي</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript خاص بإدارة الطلبات -->
    <script>
        // البحث والفلترة
        document.getElementById('searchRequests').addEventListener('input', filterRequests);
        document.getElementById('filterStatus').addEventListener('change', filterRequests);
        document.getElementById('filterService').addEventListener('change', filterRequests);
        document.getElementById('filterDate').addEventListener('change', filterRequests);
        document.getElementById('filterCity').addEventListener('change', filterRequests);
        
        function filterRequests() {
            const searchTerm = document.getElementById('searchRequests').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const serviceFilter = document.getElementById('filterService').value;
            const dateFilter = document.getElementById('filterDate').value;
            const cityFilter = document.getElementById('filterCity').value;
            const rows = document.querySelectorAll('#requestsTable tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const status = row.dataset.status;
                const service = row.dataset.service;
                const date = row.dataset.date;
                const city = row.dataset.city;
                
                const matchesSearch = text.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;
                const matchesService = !serviceFilter || service === serviceFilter;
                const matchesDate = !dateFilter || date === dateFilter;
                const matchesCity = !cityFilter || city.includes(cityFilter);
                
                if (matchesSearch && matchesStatus && matchesService && matchesDate && matchesCity) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchRequests').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterService').value = '';
            document.getElementById('filterDate').value = '';
            document.getElementById('filterCity').value = '';
            filterRequests();
        }
        
        // وظائف إدارة الطلبات
        function updateStatus(requestId, newStatus) {
            if (confirm('هل أنت متأكد من تغيير حالة هذا الطلب؟')) {
                // TODO: تنفيذ تحديث الحالة
                showNotification('سيتم تنفيذ تحديث حالة الطلب قريباً', 'info');
            }
        }
        
        function contactCustomer(phone) {
            if (confirm('هل تريد الاتصال بالعميل على الرقم: ' + phone + '؟')) {
                window.open('tel:' + phone);
            }
        }
        
        function exportRequests() {
            // TODO: تنفيذ تصدير البيانات
            showNotification('سيتم تنفيذ تصدير البيانات قريباً', 'info');
        }
        
        // تحديث تلقائي للطلبات كل 30 ثانية
        setInterval(function() {
            // TODO: تحديث البيانات تلقائياً
        }, 30000);
    </script>
</body>
</html>
