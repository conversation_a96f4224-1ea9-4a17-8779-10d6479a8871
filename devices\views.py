from django.shortcuts import render
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Q
from .models import Device, DeviceData, ControlCommand
from .serializers import (
    DeviceSerializer, DeviceCreateSerializer, DeviceUpdateSerializer,
    DeviceDataSerializer, DeviceDataCreateSerializer,
    ControlCommandSerializer, ControlCommandCreateSerializer,
    DeviceStatsSerializer, DeviceDetailSerializer
)


class DeviceListCreateAPIView(generics.ListCreateAPIView):
    """
    List all devices for the authenticated user or create a new device
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin:
            return Device.objects.all().select_related('user')
        return Device.objects.filter(user=user).select_related('user')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DeviceCreateSerializer
        return DeviceSerializer


class DeviceDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a device
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin:
            return Device.objects.all().select_related('user')
        return Device.objects.filter(user=user).select_related('user')

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return DeviceUpdateSerializer
        elif self.request.method == 'GET':
            return DeviceDetailSerializer
        return DeviceSerializer


class DeviceDataListCreateAPIView(generics.ListCreateAPIView):
    """
    List device data or create new data points
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        device_id = self.kwargs.get('device_id')

        if user.is_admin:
            queryset = DeviceData.objects.all()
        else:
            queryset = DeviceData.objects.filter(device__user=user)

        if device_id:
            queryset = queryset.filter(device_id=device_id)

        return queryset.select_related('device').order_by('-timestamp')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DeviceDataCreateSerializer
        return DeviceDataSerializer


class ControlCommandListCreateAPIView(generics.ListCreateAPIView):
    """
    List control commands or create new commands
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        device_id = self.kwargs.get('device_id')

        if user.is_admin:
            queryset = ControlCommand.objects.all()
        else:
            queryset = ControlCommand.objects.filter(device__user=user)

        if device_id:
            queryset = queryset.filter(device_id=device_id)

        return queryset.select_related('device', 'issued_by').order_by('-timestamp')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ControlCommandCreateSerializer
        return ControlCommandSerializer


class ControlCommandDetailAPIView(generics.RetrieveUpdateAPIView):
    """
    Retrieve or update a control command
    """
    serializer_class = ControlCommandSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin:
            return ControlCommand.objects.all().select_related('device', 'issued_by')
        return ControlCommand.objects.filter(device__user=user).select_related('device', 'issued_by')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def device_stats_api(request):
    """
    Get device statistics for the authenticated user
    """
    user = request.user

    if user.is_admin:
        devices = Device.objects.all()
    else:
        devices = Device.objects.filter(user=user)

    total_devices = devices.count()
    online_devices = devices.filter(status='online').count()
    offline_devices = devices.filter(status='offline').count()

    # Device types breakdown
    device_types = devices.values('device_type').annotate(
        count=Count('device_type')
    ).order_by('-count')

    device_types_dict = {item['device_type']: item['count'] for item in device_types}

    # Recent activity (recent commands)
    recent_commands = ControlCommand.objects.filter(
        device__in=devices
    ).select_related('device', 'issued_by').order_by('-timestamp')[:10]

    recent_activity = [
        {
            'action': f"{cmd.get_command_type_display()} - {cmd.device.name}",
            'timestamp': cmd.timestamp,
            'user': cmd.issued_by.username,
            'status': cmd.status
        }
        for cmd in recent_commands
    ]

    stats_data = {
        'total_devices': total_devices,
        'online_devices': online_devices,
        'offline_devices': offline_devices,
        'device_types': device_types_dict,
        'recent_activity': recent_activity
    }

    serializer = DeviceStatsSerializer(stats_data)
    return Response(serializer.data)
