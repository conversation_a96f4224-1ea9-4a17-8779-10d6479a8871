<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دخول الإدارة - IQHome</title>

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Simple Admin CSS -->
    <link href="/static/css/simple-admin.css" rel="stylesheet">
    
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-card {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-cyber);
            animation: scanLine 4s ease-in-out infinite;
        }
        
        .login-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-hologram);
            opacity: 0.05;
            pointer-events: none;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-logo i {
            font-size: 4rem;
            background: var(--gradient-cyber);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            animation: hologramFlicker 3s ease-in-out infinite;
        }
        
        .login-title {
            font-size: 2.5rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 0.5rem;
            background: var(--gradient-cyber);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .login-subtitle {
            text-align: center;
            color: var(--cyber-blue);
            margin-bottom: 2rem;
            font-weight: 300;
            opacity: 0.8;
        }
        
        .form-floating-cyber {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .form-floating-cyber input {
            background: rgba(10, 10, 15, 0.8);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            color: var(--cyber-blue);
            padding: 1.5rem 1rem 0.5rem 3rem;
            font-family: var(--font-tech);
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .form-floating-cyber input:focus {
            border-color: var(--cyber-blue);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
            background: rgba(10, 10, 15, 0.9);
            outline: none;
        }
        
        .form-floating-cyber label {
            position: absolute;
            top: 50%;
            right: 3rem;
            transform: translateY(-50%);
            color: rgba(0, 212, 255, 0.6);
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .form-floating-cyber input:focus + label,
        .form-floating-cyber input:not(:placeholder-shown) + label {
            top: 0.5rem;
            font-size: 0.8rem;
            color: var(--cyber-blue);
        }
        
        .form-floating-cyber i {
            position: absolute;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            color: var(--cyber-blue);
            font-size: 1.2rem;
            z-index: 2;
        }
        
        .login-btn {
            width: 100%;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.8s;
        }
        
        .login-btn:hover::before {
            left: 100%;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .remember-me input[type="checkbox"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid var(--cyber-blue);
            border-radius: 4px;
            background: transparent;
            margin-left: 0.5rem;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .remember-me input[type="checkbox"]:checked {
            background: var(--cyber-blue);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }
        
        .remember-me input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--void-black);
            font-weight: bold;
            font-size: 12px;
        }
        
        .remember-me label {
            color: var(--cyber-blue);
            cursor: pointer;
            font-weight: 500;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(0, 212, 255, 0.2);
        }
        
        .login-footer a {
            color: var(--cyber-blue);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .login-footer a:hover {
            color: var(--quantum-gold);
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1rem;
            color: var(--electric-green);
            font-size: 0.9rem;
        }
        
        .security-badge i {
            margin-left: 0.5rem;
            animation: pulse 2s ease-in-out infinite;
        }
        
        /* تأثيرات الجسيمات */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--cyber-blue);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
            opacity: 0.6;
        }
        
        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            90% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                transform: translateY(-100px) scale(0);
                opacity: 0;
            }
        }
        
        /* تأثير الخطأ */
        .error-message {
            background: rgba(255, 0, 110, 0.1);
            border: 1px solid var(--plasma-pink);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: var(--plasma-pink);
            text-align: center;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        /* تأثير التحميل */
        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid var(--cyber-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- جسيمات متحركة -->
        <div class="particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="login-card fade-in-cyber">
            <!-- شعار الشركة -->
            <div class="login-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            
            <!-- عنوان تسجيل الدخول -->
            <h1 class="login-title">مركز التحكم</h1>
            <p class="login-subtitle">IQHome Control Center</p>
            
            <!-- رسائل الخطأ -->
            {% if form.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    بيانات تسجيل الدخول غير صحيحة
                </div>
            {% endif %}
            
            <!-- نموذج تسجيل الدخول -->
            <form method="post" id="loginForm">
                {% csrf_token %}
                
                <div class="form-floating-cyber">
                    <i class="fas fa-user"></i>
                    <input type="text" name="username" placeholder=" " required>
                    <label>اسم المستخدم</label>
                </div>
                
                <div class="form-floating-cyber">
                    <i class="fas fa-lock"></i>
                    <input type="password" name="password" placeholder=" " required>
                    <label>كلمة المرور</label>
                </div>
                
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">تذكرني</label>
                </div>
                
                <button type="submit" class="btn btn-cyber login-btn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    دخول مركز التحكم
                </button>
                
                <!-- حالة التحميل -->
                <div class="loading" id="loading">
                    <div class="loading-spinner"></div>
                    <p>جاري التحقق من البيانات...</p>
                </div>
            </form>
            
            <!-- شارة الأمان -->
            <div class="security-badge">
                <i class="fas fa-shield-check"></i>
                اتصال آمن ومشفر
            </div>
            
            <!-- تذييل تسجيل الدخول -->
            <div class="login-footer">
                <p>
                    <a href="/">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للموقع الرئيسي
                    </a>
                </p>
                <p class="mt-2">
                    <small>
                        &copy; 2025 IQHome. جميع الحقوق محفوظة.
                    </small>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تأثيرات تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const loading = document.getElementById('loading');
            const loginCard = document.querySelector('.login-card');
            
            // تأثير الظهور التدريجي
            setTimeout(() => {
                loginCard.style.opacity = '1';
                loginCard.style.transform = 'translateY(0) scale(1)';
            }, 300);
            
            // معالجة إرسال النموذج
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                
                // إظهار حالة التحميل
                submitBtn.style.display = 'none';
                loading.style.display = 'block';
                
                // تأثير الاهتزاز للبطاقة
                loginCard.style.animation = 'pulse 0.5s ease-in-out';
                
                // إذا كانت هناك أخطاء، إخفاء التحميل
                setTimeout(() => {
                    if (form.querySelector('.error-message')) {
                        submitBtn.style.display = 'block';
                        loading.style.display = 'none';
                    }
                }, 2000);
            });
            
            // تأثيرات الحقول
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.3)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                    this.parentElement.style.boxShadow = 'none';
                });
            });
            
            // تأثير الكتابة
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderColor = 'var(--electric-green)';
                        this.style.boxShadow = '0 0 10px rgba(16, 245, 74, 0.3)';
                    } else {
                        this.style.borderColor = 'rgba(0, 212, 255, 0.3)';
                        this.style.boxShadow = 'none';
                    }
                });
            });
        });
        
        // تأثير الجسيمات المتحركة
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            particle.style.opacity = Math.random() * 0.5 + 0.3;
            
            document.querySelector('.particles').appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }
        
        // إنشاء جسيمات جديدة كل ثانية
        setInterval(createParticle, 1000);
        
        // تأثير الماوس
        document.addEventListener('mousemove', function(e) {
            const loginCard = document.querySelector('.login-card');
            const rect = loginCard.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;
            
            loginCard.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
        
        document.addEventListener('mouseleave', function() {
            const loginCard = document.querySelector('.login-card');
            loginCard.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
        });
    </script>
</body>
</html>
