from django.db import models
from django.utils import timezone
import uuid


class Service(models.Model):
    """
    نموذج الخدمات التي تقدمها الشركة
    """
    
    SERVICE_CATEGORIES = [
        ('installation', 'تركيب المنازل الذكية'),
        ('maintenance', 'صيانة وإصلاح'),
        ('consultation', 'استشارات تقنية'),
        ('upgrade', 'ترقية الأنظمة'),
        ('monitoring', 'خدمات المراقبة'),
        ('training', 'التدريب والتأهيل'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name_ar = models.CharField(max_length=200, verbose_name="اسم الخدمة بالعربية")
    name_en = models.CharField(max_length=200, verbose_name="اسم الخدمة بالإنجليزية")
    description_ar = models.TextField(verbose_name="وصف الخدمة بالعربية")
    description_en = models.TextField(verbose_name="وصف الخدمة بالإنجليزية")
    category = models.CharField(max_length=20, choices=SERVICE_CATEGORIES, verbose_name="فئة الخدمة")
    
    # تفاصيل الخدمة
    price_range_min = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="أقل سعر")
    price_range_max = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="أعلى سعر")
    duration_hours = models.IntegerField(verbose_name="مدة التنفيذ بالساعات")
    
    # الصور والملفات
    main_image = models.ImageField(upload_to='services/', verbose_name="الصورة الرئيسية")
    gallery_images = models.JSONField(default=list, blank=True, verbose_name="معرض الصور")
    
    # الميزات والمواصفات
    features = models.JSONField(default=list, verbose_name="الميزات")
    requirements = models.JSONField(default=list, verbose_name="المتطلبات")
    
    # حالة الخدمة
    is_active = models.BooleanField(default=True, verbose_name="نشطة")
    is_featured = models.BooleanField(default=False, verbose_name="خدمة مميزة")
    
    # التواريخ
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'services'
        verbose_name = 'خدمة'
        verbose_name_plural = 'الخدمات'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name_ar
    
    @property
    def price_range_display(self):
        """عرض نطاق السعر"""
        return f"{self.price_range_min:,.0f} - {self.price_range_max:,.0f} د.ع"


class ServiceRequest(models.Model):
    """
    نموذج طلبات الخدمات من العملاء
    """
    
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('reviewing', 'قيد المراجعة'),
        ('approved', 'موافق عليه'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]
    
    # معرف الطلب
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    request_number = models.CharField(max_length=20, unique=True, editable=False, verbose_name="رقم الطلب")
    
    # الخدمة المطلوبة
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='requests', verbose_name="الخدمة")
    
    # بيانات العميل
    customer_name = models.CharField(max_length=100, verbose_name="اسم العميل")
    customer_phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    customer_email = models.EmailField(verbose_name="البريد الإلكتروني")
    customer_address = models.TextField(verbose_name="العنوان")
    customer_city = models.CharField(max_length=50, verbose_name="المدينة")
    
    # تفاصيل الطلب
    description = models.TextField(verbose_name="وصف الطلب")
    preferred_date = models.DateField(verbose_name="التاريخ المفضل")
    preferred_time = models.TimeField(verbose_name="الوقت المفضل")
    
    # حالة الطلب
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الطلب")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name="الأولوية")
    
    # التكلفة المقدرة
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="التكلفة المقدرة")
    final_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="التكلفة النهائية")
    
    # الملاحظات
    customer_notes = models.TextField(blank=True, verbose_name="ملاحظات العميل")
    admin_notes = models.TextField(blank=True, verbose_name="ملاحظات الإدارة")
    
    # التواريخ
    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإنجاز")
    
    # الموظف المسؤول
    assigned_to = models.ForeignKey(
        'accounts.User', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='assigned_requests',
        verbose_name="المسؤول"
    )
    
    class Meta:
        db_table = 'service_requests'
        verbose_name = 'طلب خدمة'
        verbose_name_plural = 'طلبات الخدمات'
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.request_number:
            # إنشاء رقم طلب: IQ-YYYYMMDD-XXXX
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
            last_request = ServiceRequest.objects.filter(
                request_number__startswith=f'IQ-{date_str}'
            ).order_by('-request_number').first()
            
            if last_request:
                last_num = int(last_request.request_number.split('-')[-1])
                new_num = last_num + 1
            else:
                new_num = 1
            
            self.request_number = f'IQ-{date_str}-{new_num:04d}'
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.request_number} - {self.customer_name}"
    
    @property
    def is_pending(self):
        """التحقق من كون الطلب في الانتظار"""
        return self.status == 'pending'
    
    @property
    def is_completed(self):
        """التحقق من كون الطلب مكتمل"""
        return self.status == 'completed'


class ServiceGallery(models.Model):
    """
    معرض أعمال الشركة
    """
    
    PROJECT_TYPES = [
        ('residential', 'سكني'),
        ('commercial', 'تجاري'),
        ('industrial', 'صناعي'),
        ('government', 'حكومي'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title_ar = models.CharField(max_length=200, verbose_name="عنوان المشروع بالعربية")
    title_en = models.CharField(max_length=200, verbose_name="عنوان المشروع بالإنجليزية")
    description_ar = models.TextField(verbose_name="وصف المشروع بالعربية")
    description_en = models.TextField(verbose_name="وصف المشروع بالإنجليزية")
    
    project_type = models.CharField(max_length=20, choices=PROJECT_TYPES, verbose_name="نوع المشروع")
    location = models.CharField(max_length=100, verbose_name="الموقع")
    completion_date = models.DateField(verbose_name="تاريخ الإنجاز")
    
    # الصور
    main_image = models.ImageField(upload_to='gallery/', verbose_name="الصورة الرئيسية")
    before_images = models.JSONField(default=list, verbose_name="صور قبل التنفيذ")
    after_images = models.JSONField(default=list, verbose_name="صور بعد التنفيذ")
    
    # الخدمات المستخدمة
    services_used = models.ManyToManyField(Service, verbose_name="الخدمات المستخدمة")
    
    # التفاصيل التقنية
    technologies_used = models.JSONField(default=list, verbose_name="التقنيات المستخدمة")
    project_duration = models.IntegerField(verbose_name="مدة المشروع بالأيام")
    
    # حالة العرض
    is_featured = models.BooleanField(default=False, verbose_name="مشروع مميز")
    is_published = models.BooleanField(default=True, verbose_name="منشور")
    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'service_gallery'
        verbose_name = 'مشروع في المعرض'
        verbose_name_plural = 'معرض الأعمال'
        ordering = ['-completion_date']
    
    def __str__(self):
        return self.title_ar


class ContactMessage(models.Model):
    """
    رسائل التواصل من الموقع
    """
    
    MESSAGE_TYPES = [
        ('inquiry', 'استفسار عام'),
        ('quote', 'طلب عرض سعر'),
        ('complaint', 'شكوى'),
        ('suggestion', 'اقتراح'),
        ('partnership', 'شراكة'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, verbose_name="الاسم")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    subject = models.CharField(max_length=200, verbose_name="الموضوع")
    message = models.TextField(verbose_name="الرسالة")
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='inquiry', verbose_name="نوع الرسالة")
    
    # حالة الرسالة
    is_read = models.BooleanField(default=False, verbose_name="مقروءة")
    is_replied = models.BooleanField(default=False, verbose_name="تم الرد")
    
    # الرد
    reply_message = models.TextField(blank=True, verbose_name="رسالة الرد")
    replied_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="تم الرد بواسطة"
    )
    replied_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الرد")
    
    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإرسال")
    
    class Meta:
        db_table = 'contact_messages'
        verbose_name = 'رسالة تواصل'
        verbose_name_plural = 'رسائل التواصل'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.subject}"
