from django.shortcuts import render
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Q
from django.utils import timezone
from .models import Notification, SystemLog
from .serializers import (
    NotificationSerializer, NotificationCreateSerializer,
    SystemLogSerializer, SystemLogCreateSerializer,
    NotificationStatsSerializer, UserNotificationSerializer,
    BulkNotificationActionSerializer
)


class NotificationListCreateAPIView(generics.ListCreateAPIView):
    """
    List notifications for the authenticated user or create a new notification (admin only)
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.is_admin:
            # <PERSON><PERSON> can see all notifications
            queryset = Notification.objects.all()
        else:
            # Users see their own notifications and broadcast notifications
            queryset = Notification.objects.filter(
                Q(target_user=user) | Q(is_broadcast=True)
            )

        # Filter out expired notifications
        queryset = queryset.filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        )

        return queryset.select_related('target_user', 'related_device', 'related_ticket').order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return NotificationCreateSerializer
        elif self.request.user.is_admin:
            return NotificationSerializer
        return UserNotificationSerializer

    def perform_create(self, serializer):
        # Only admins can create notifications
        if not self.request.user.is_admin:
            raise permissions.PermissionDenied("Only admins can create notifications")
        serializer.save()


class NotificationDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a notification
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.is_admin:
            return Notification.objects.all()
        else:
            return Notification.objects.filter(
                Q(target_user=user) | Q(is_broadcast=True)
            )

    def get_serializer_class(self):
        if self.request.user.is_admin:
            return NotificationSerializer
        return UserNotificationSerializer


class SystemLogListAPIView(generics.ListAPIView):
    """
    List system logs (admin only)
    """
    serializer_class = SystemLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if not self.request.user.is_admin:
            return SystemLog.objects.none()

        return SystemLog.objects.all().select_related('performed_by').order_by('-timestamp')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read_api(request, notification_id):
    """
    Mark a notification as read
    """
    user = request.user

    try:
        if user.is_admin:
            notification = Notification.objects.get(id=notification_id)
        else:
            notification = Notification.objects.get(
                id=notification_id,
                target_user=user
            )
    except Notification.DoesNotExist:
        return Response({'error': 'Notification not found'}, status=status.HTTP_404_NOT_FOUND)

    notification.mark_as_read()

    return Response({
        'message': 'Notification marked as read',
        'notification_id': notification_id,
        'read_at': notification.read_at
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_notification_action_api(request):
    """
    Perform bulk actions on notifications
    """
    serializer = BulkNotificationActionSerializer(data=request.data, context={'request': request})

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    notification_ids = serializer.validated_data['notification_ids']
    action = serializer.validated_data['action']

    user = request.user

    if user.is_admin:
        notifications = Notification.objects.filter(id__in=notification_ids)
    else:
        notifications = Notification.objects.filter(
            id__in=notification_ids,
            target_user=user
        )

    if action == 'mark_read':
        updated_count = 0
        for notification in notifications:
            if notification.status == 'unread':
                notification.mark_as_read()
                updated_count += 1

        return Response({
            'message': f'{updated_count} notifications marked as read',
            'action': action,
            'count': updated_count
        })

    elif action == 'archive':
        updated_count = 0
        for notification in notifications:
            notification.archive()
            updated_count += 1

        return Response({
            'message': f'{updated_count} notifications archived',
            'action': action,
            'count': updated_count
        })

    elif action == 'delete':
        count = notifications.count()
        notifications.delete()

        return Response({
            'message': f'{count} notifications deleted',
            'action': action,
            'count': count
        })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def notification_stats_api(request):
    """
    Get notification statistics
    """
    user = request.user

    if user.is_admin:
        notifications = Notification.objects.all()
    else:
        notifications = Notification.objects.filter(
            Q(target_user=user) | Q(is_broadcast=True)
        )

    # Filter out expired notifications
    notifications = notifications.filter(
        Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
    )

    total_notifications = notifications.count()
    unread_notifications = notifications.filter(status='unread').count()
    read_notifications = notifications.filter(status='read').count()

    # Notifications by type
    notifications_by_type = notifications.values('notification_type').annotate(
        count=Count('notification_type')
    ).order_by('-count')
    type_dict = {item['notification_type']: item['count'] for item in notifications_by_type}

    # Recent notifications
    recent_notifications = notifications.order_by('-created_at')[:10]
    recent_notifications_data = [
        {
            'id': str(notification.id),
            'title': notification.title,
            'notification_type': notification.notification_type,
            'status': notification.status,
            'created_at': notification.created_at,
            'target_user': notification.target_user.username if notification.target_user else 'Broadcast'
        }
        for notification in recent_notifications
    ]

    stats_data = {
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'read_notifications': read_notifications,
        'notifications_by_type': type_dict,
        'recent_notifications': recent_notifications_data
    }

    serializer = NotificationStatsSerializer(stats_data)
    return Response(serializer.data)
