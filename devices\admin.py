from django.contrib import admin
from .models import Device, DeviceD<PERSON>, ControlCommand


@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):
    """
    Device admin interface
    """

    list_display = ('name', 'device_type', 'user', 'status', 'location', 'mac_address', 'is_active', 'created_at')
    list_filter = ('device_type', 'status', 'is_active', 'created_at', 'manufacturer')
    search_fields = ('name', 'mac_address', 'user__username', 'location', 'model_number')
    ordering = ('-created_at',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'device_type', 'user', 'location', 'is_active')
        }),
        ('Network Configuration', {
            'fields': ('mac_address', 'ip_address', 'status')
        }),
        ('Device Specifications', {
            'fields': ('manufacturer', 'model_number', 'firmware_version'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_seen'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at', 'last_seen')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(DeviceData)
class DeviceDataAdmin(admin.ModelAdmin):
    """
    Device data admin interface
    """

    list_display = ('device', 'key', 'value', 'unit', 'timestamp')
    list_filter = ('key', 'timestamp', 'device__device_type')
    search_fields = ('device__name', 'key', 'value')
    ordering = ('-timestamp',)

    fieldsets = (
        ('Data Point', {
            'fields': ('device', 'key', 'value', 'unit')
        }),
        ('Timestamp', {
            'fields': ('timestamp',)
        }),
    )

    readonly_fields = ('timestamp',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('device', 'device__user')


@admin.register(ControlCommand)
class ControlCommandAdmin(admin.ModelAdmin):
    """
    Control command admin interface
    """

    list_display = ('device', 'command_type', 'status', 'issued_by', 'timestamp', 'completed_at')
    list_filter = ('command_type', 'status', 'timestamp')
    search_fields = ('device__name', 'issued_by__username', 'command_type')
    ordering = ('-timestamp',)

    fieldsets = (
        ('Command Details', {
            'fields': ('device', 'command_type', 'command_data', 'issued_by')
        }),
        ('Status', {
            'fields': ('status', 'response_data', 'error_message')
        }),
        ('Timestamps', {
            'fields': ('timestamp', 'sent_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('timestamp', 'sent_at', 'completed_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('device', 'device__user', 'issued_by')
