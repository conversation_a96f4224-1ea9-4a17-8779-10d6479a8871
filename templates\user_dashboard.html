{% extends 'base.html' %}

{% block title %}Dashboard - IQHome{% endblock %}

{% block extra_css %}
<style>
.device-card {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.device-card:hover {
    transform: translateY(-3px);
}

.device-status {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.device-status.online {
    background-color: #28a745;
}

.device-status.offline {
    background-color: #dc3545;
}

.device-status.error {
    background-color: #ffc107;
}

.quick-stats {
    background: linear-gradient(135deg, #007BFF, #0056b3);
    color: white;
    border-radius: 15px;
}

.device-control {
    display: none;
}

.device-card.expanded .device-control {
    display: block;
}

.chart-container {
    position: relative;
    height: 250px;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-home me-3"></i>Welcome back, {{ user.first_name|default:user.username }}!</h1>
        <p class="text-muted">Manage your smart home devices</p>
    </div>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
            <i class="fas fa-plus me-2"></i>Add Device
        </button>
        <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>
</div>

<!-- Quick Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card quick-stats">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h3 id="total-devices">{{ stats.total_devices|default:0 }}</h3>
                        <p class="mb-0">Total Devices</p>
                    </div>
                    <div class="col-md-3">
                        <h3 id="online-devices">{{ stats.online_devices|default:0 }}</h3>
                        <p class="mb-0">Online</p>
                    </div>
                    <div class="col-md-3">
                        <h3 id="energy-usage">{{ stats.energy_usage|default:"0 kWh" }}</h3>
                        <p class="mb-0">Energy Today</p>
                    </div>
                    <div class="col-md-3">
                        <h3 id="automation-rules">{{ stats.automation_rules|default:0 }}</h3>
                        <p class="mb-0">Automations</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Devices Grid -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-microchip me-2"></i>My Devices</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary active" onclick="filterDevices('all')">All</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="filterDevices('online')">Online</button>
                    <button type="button" class="btn btn-outline-secondary" onclick="filterDevices('offline')">Offline</button>
                </div>
            </div>
            <div class="card-body">
                <div class="row" id="devices-grid">
                    <!-- Devices will be loaded here -->
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading devices...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>Energy Usage</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="energyChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-bell me-2"></i>Recent Notifications</h5>
            </div>
            <div class="card-body">
                <div id="notifications-list">
                    <!-- Notifications will be loaded here -->
                    <p class="text-muted">No new notifications</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Action</th>
                                <th>Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="activity-table">
                            <!-- Activity will be loaded here -->
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Device Modal -->
<div class="modal fade" id="addDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDeviceForm">
                    <div class="mb-3">
                        <label for="deviceName" class="form-label">Device Name</label>
                        <input type="text" class="form-control" id="deviceName" required>
                    </div>
                    <div class="mb-3">
                        <label for="deviceType" class="form-label">Device Type</label>
                        <select class="form-control" id="deviceType" required>
                            <option value="">Select type...</option>
                            <option value="light">Smart Light</option>
                            <option value="switch">Smart Switch</option>
                            <option value="sensor">Sensor</option>
                            <option value="camera">Security Camera</option>
                            <option value="thermostat">Thermostat</option>
                            <option value="lock">Smart Lock</option>
                            <option value="outlet">Smart Outlet</option>
                            <option value="water">Water Controller</option>
                            <option value="energy">Energy Monitor</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="deviceLocation" class="form-label">Location</label>
                        <input type="text" class="form-control" id="deviceLocation" placeholder="e.g., Living Room">
                    </div>
                    <div class="mb-3">
                        <label for="macAddress" class="form-label">MAC Address</label>
                        <input type="text" class="form-control" id="macAddress" placeholder="XX:XX:XX:XX:XX:XX" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addDevice()">Add Device</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dashboard functionality
let energyChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadDevices();
    loadNotifications();
    loadRecentActivity();
    
    // Auto-refresh every 2 minutes
    setInterval(refreshDashboard, 120000);
});

function initializeCharts() {
    // Energy Usage Chart
    const energyCtx = document.getElementById('energyChart').getContext('2d');
    energyChart = new Chart(energyCtx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Energy (kWh)',
                data: [12, 15, 10, 18, 14, 20, 16],
                borderColor: '#007BFF',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function loadDevices() {
    // Simulate loading devices
    const devicesGrid = document.getElementById('devices-grid');
    
    setTimeout(() => {
        devicesGrid.innerHTML = `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card device-card" onclick="toggleDevice(this)">
                    <div class="device-status online"></div>
                    <div class="card-body text-center">
                        <i class="fas fa-lightbulb fa-2x text-warning mb-2"></i>
                        <h6>Living Room Light</h6>
                        <small class="text-muted">Smart Light</small>
                        <div class="device-control mt-2">
                            <button class="btn btn-sm btn-outline-primary">Toggle</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card device-card" onclick="toggleDevice(this)">
                    <div class="device-status online"></div>
                    <div class="card-body text-center">
                        <i class="fas fa-thermometer-half fa-2x text-info mb-2"></i>
                        <h6>Kitchen Sensor</h6>
                        <small class="text-muted">Temperature: 22°C</small>
                        <div class="device-control mt-2">
                            <button class="btn btn-sm btn-outline-info">View Data</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card device-card" onclick="toggleDevice(this)">
                    <div class="device-status offline"></div>
                    <div class="card-body text-center">
                        <i class="fas fa-video fa-2x text-secondary mb-2"></i>
                        <h6>Front Door Camera</h6>
                        <small class="text-muted">Security Camera</small>
                        <div class="device-control mt-2">
                            <button class="btn btn-sm btn-outline-secondary">Reconnect</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}

function loadNotifications() {
    // Simulate loading notifications
    const notificationsList = document.getElementById('notifications-list');
    
    setTimeout(() => {
        notificationsList.innerHTML = `
            <div class="alert alert-info alert-sm">
                <i class="fas fa-info-circle me-2"></i>
                Kitchen sensor detected high temperature
                <small class="d-block text-muted">5 minutes ago</small>
            </div>
            <div class="alert alert-warning alert-sm">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Front door camera is offline
                <small class="d-block text-muted">1 hour ago</small>
            </div>
        `;
    }, 1500);
}

function loadRecentActivity() {
    // Simulate loading recent activity
    const activityTable = document.getElementById('activity-table');
    
    setTimeout(() => {
        activityTable.innerHTML = `
            <tr>
                <td>Living Room Light</td>
                <td>Turned On</td>
                <td>2 minutes ago</td>
                <td><span class="badge bg-success">Success</span></td>
            </tr>
            <tr>
                <td>Kitchen Sensor</td>
                <td>Data Reading</td>
                <td>5 minutes ago</td>
                <td><span class="badge bg-success">Success</span></td>
            </tr>
            <tr>
                <td>Front Door Camera</td>
                <td>Connection Lost</td>
                <td>1 hour ago</td>
                <td><span class="badge bg-danger">Failed</span></td>
            </tr>
        `;
    }, 2000);
}

function toggleDevice(card) {
    card.classList.toggle('expanded');
}

function filterDevices(filter) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Filter devices (implementation would filter actual device cards)
    console.log('Filtering devices by:', filter);
}

function refreshDashboard() {
    loadDevices();
    loadNotifications();
    loadRecentActivity();
    
    // Show refresh indicator
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const icon = refreshBtn.querySelector('i');
    icon.classList.add('fa-spin');
    
    setTimeout(() => {
        icon.classList.remove('fa-spin');
    }, 2000);
}

function addDevice() {
    // Get form data
    const name = document.getElementById('deviceName').value;
    const type = document.getElementById('deviceType').value;
    const location = document.getElementById('deviceLocation').value;
    const macAddress = document.getElementById('macAddress').value;
    
    if (!name || !type || !macAddress) {
        alert('Please fill in all required fields');
        return;
    }
    
    // Simulate adding device
    alert('Device added successfully!');
    
    // Close modal and refresh
    const modal = bootstrap.Modal.getInstance(document.getElementById('addDeviceModal'));
    modal.hide();
    
    // Reset form
    document.getElementById('addDeviceForm').reset();
    
    // Refresh devices
    loadDevices();
}
</script>
{% endblock %}
