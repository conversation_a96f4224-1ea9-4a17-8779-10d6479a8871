from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class Notification(models.Model):
    """
    Notification model for system alerts and user messages
    """

    TYPE_CHOICES = [
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('success', 'Success'),
        ('device_alert', 'Device Alert'),
        ('system', 'System Notification'),
        ('support', 'Support Update'),
    ]

    STATUS_CHOICES = [
        ('unread', 'Unread'),
        ('read', 'Read'),
        ('archived', 'Archived'),
    ]

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Notification content
    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='info')

    # Target user (null means broadcast to all users)
    target_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='unread')
    is_broadcast = models.BooleanField(default=False)  # True for system-wide notifications

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    read_at = models.DateTimeField(blank=True, null=True)
    expires_at = models.DateTimeField(blank=True, null=True)  # Optional expiration

    # Related objects
    related_device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )
    related_ticket = models.ForeignKey(
        'support.SupportTicket',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )

    # Additional data (JSON field for flexible content)
    extra_data = models.JSONField(blank=True, null=True)

    class Meta:
        db_table = 'notifications'
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['target_user', 'status', '-created_at']),
            models.Index(fields=['is_broadcast', '-created_at']),
        ]

    def __str__(self):
        target = self.target_user.username if self.target_user else "Broadcast"
        return f"{self.title} - {target}"

    def mark_as_read(self):
        """Mark notification as read"""
        if self.status == 'unread':
            self.status = 'read'
            self.read_at = timezone.now()
            self.save(update_fields=['status', 'read_at'])

    def archive(self):
        """Archive the notification"""
        self.status = 'archived'
        self.save(update_fields=['status'])

    @property
    def is_expired(self):
        """Check if notification has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class SystemLog(models.Model):
    """
    System activity log for audit trail and monitoring
    """

    ACTION_TYPES = [
        ('user_login', 'User Login'),
        ('user_logout', 'User Logout'),
        ('user_created', 'User Created'),
        ('user_updated', 'User Updated'),
        ('user_deleted', 'User Deleted'),
        ('device_added', 'Device Added'),
        ('device_updated', 'Device Updated'),
        ('device_removed', 'Device Removed'),
        ('device_command', 'Device Command'),
        ('ticket_created', 'Support Ticket Created'),
        ('ticket_updated', 'Support Ticket Updated'),
        ('system_error', 'System Error'),
        ('admin_action', 'Admin Action'),
        ('security_event', 'Security Event'),
    ]

    LEVEL_CHOICES = [
        ('debug', 'Debug'),
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
    ]

    # Log entry details
    action = models.CharField(max_length=30, choices=ACTION_TYPES)
    level = models.CharField(max_length=10, choices=LEVEL_CHOICES, default='info')
    description = models.TextField()

    # User who performed the action (null for system actions)
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='system_logs'
    )

    # Timestamp and metadata
    timestamp = models.DateTimeField(default=timezone.now)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    # Additional context data
    extra_data = models.JSONField(blank=True, null=True)

    class Meta:
        db_table = 'system_logs'
        verbose_name = 'System Log'
        verbose_name_plural = 'System Logs'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['action', '-timestamp']),
            models.Index(fields=['performed_by', '-timestamp']),
            models.Index(fields=['level', '-timestamp']),
        ]

    def __str__(self):
        user = self.performed_by.username if self.performed_by else "System"
        return f"{self.get_action_display()} by {user} at {self.timestamp}"
