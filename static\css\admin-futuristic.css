/* IQHome Admin Panel - Futuristic Design من عالم آخر */

/* خطوط عربية تقنية فخمة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
    /* ألوان مستقبلية مبهرة */
    --cyber-blue: #00D4FF;
    --neon-purple: #8B5CF6;
    --electric-green: #10F54A;
    --plasma-pink: #FF006E;
    --quantum-gold: #FFD700;
    --void-black: #0A0A0F;
    --space-gray: #1A1A2E;
    --nebula-purple: #16213E;
    --cosmic-blue: #0F3460;
    --stellar-white: #E94560;
    
    /* تدرجات مستقبلية */
    --gradient-cyber: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 50%, #FF006E 100%);
    --gradient-matrix: linear-gradient(45deg, #10F54A 0%, #00D4FF 50%, #8B5CF6 100%);
    --gradient-plasma: linear-gradient(90deg, #FF006E 0%, #8B5CF6 50%, #00D4FF 100%);
    --gradient-void: linear-gradient(135deg, #0A0A0F 0%, #1A1A2E 50%, #16213E 100%);
    --gradient-hologram: linear-gradient(45deg, 
        transparent 25%, 
        rgba(0, 212, 255, 0.1) 25%, 
        rgba(0, 212, 255, 0.1) 50%, 
        transparent 50%, 
        transparent 75%, 
        rgba(139, 92, 246, 0.1) 75%);
    
    /* ظلال مستقبلية */
    --shadow-cyber: 0 0 30px rgba(0, 212, 255, 0.5), 0 0 60px rgba(0, 212, 255, 0.3);
    --shadow-neon: 0 0 20px rgba(139, 92, 246, 0.6), 0 0 40px rgba(139, 92, 246, 0.4);
    --shadow-plasma: 0 0 25px rgba(255, 0, 110, 0.5), 0 0 50px rgba(255, 0, 110, 0.3);
    --shadow-hologram: 0 10px 40px rgba(0, 0, 0, 0.8), 0 0 80px rgba(0, 212, 255, 0.2);
    
    /* خطوط تقنية */
    --font-tech: 'IBM Plex Sans Arabic', 'Cairo', sans-serif;
    --font-display: 'Almarai', 'Noto Sans Arabic', sans-serif;
    --font-mono: 'Courier New', monospace;
    
    /* أحجام وأبعاد */
    --border-radius-cyber: 15px;
    --border-radius-card: 20px;
    --spacing-unit: 1rem;
    --animation-speed: 0.3s;
}

/* إعدادات عامة مستقبلية */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    direction: rtl;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-tech);
    background: var(--void-black);
    color: var(--cyber-blue);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* خلفية مستقبلية متحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.05) 0%, transparent 50%);
    z-index: -2;
    animation: cosmicPulse 8s ease-in-out infinite alternate;
}

/* شبكة مستقبلية */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
    animation: gridMove 20s linear infinite;
}

/* العناوين المستقبلية */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    margin-bottom: var(--spacing-unit);
}

h1 {
    font-size: 3.5rem;
    font-weight: 800;
    letter-spacing: 2px;
}

h2 {
    font-size: 2.5rem;
    font-weight: 700;
}

h3 {
    font-size: 2rem;
    font-weight: 600;
}

/* الأزرار المستقبلية */
.btn-cyber {
    background: var(--gradient-cyber);
    border: none;
    color: var(--void-black);
    font-family: var(--font-tech);
    font-weight: 600;
    padding: 15px 30px;
    border-radius: var(--border-radius-cyber);
    position: relative;
    overflow: hidden;
    transition: all var(--animation-speed) ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--shadow-cyber);
}

.btn-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.btn-cyber:hover::before {
    left: 100%;
}

.btn-cyber:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-cyber), 0 15px 40px rgba(0, 212, 255, 0.4);
}

.btn-neon {
    background: transparent;
    border: 2px solid var(--neon-purple);
    color: var(--neon-purple);
    font-family: var(--font-tech);
    font-weight: 500;
    padding: 12px 25px;
    border-radius: var(--border-radius-cyber);
    transition: all var(--animation-speed) ease;
    position: relative;
    overflow: hidden;
}

.btn-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--neon-purple);
    transition: width var(--animation-speed) ease;
    z-index: -1;
}

.btn-neon:hover::before {
    width: 100%;
}

.btn-neon:hover {
    color: var(--void-black);
    box-shadow: var(--shadow-neon);
    transform: translateY(-2px);
}

/* البطاقات المستقبلية */
.card-cyber {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: var(--border-radius-card);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    transition: all var(--animation-speed) ease;
    box-shadow: var(--shadow-hologram);
}

.card-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-cyber);
    animation: scanLine 3s ease-in-out infinite;
}

.card-cyber::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0;
    transition: opacity var(--animation-speed) ease;
    pointer-events: none;
}

.card-cyber:hover {
    transform: translateY(-10px) rotateX(5deg);
    border-color: var(--cyber-blue);
    box-shadow: var(--shadow-cyber), var(--shadow-hologram);
}

.card-cyber:hover::after {
    opacity: 1;
}

.card-cyber .card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

/* شريط التنقل المستقبلي */
.navbar-cyber {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--cyber-blue);
    padding: 1rem 0;
    position: relative;
    overflow: hidden;
}

.navbar-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0.1;
}

.navbar-brand-cyber {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 800;
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    position: relative;
}

.nav-link-cyber {
    color: var(--cyber-blue) !important;
    font-weight: 500;
    padding: 0.8rem 1.5rem !important;
    border-radius: var(--border-radius-cyber);
    transition: all var(--animation-speed) ease;
    position: relative;
    overflow: hidden;
}

.nav-link-cyber::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-cyber);
    transition: all var(--animation-speed) ease;
    transform: translateX(-50%);
}

.nav-link-cyber:hover::before,
.nav-link-cyber.active::before {
    width: 80%;
}

.nav-link-cyber:hover {
    color: var(--quantum-gold) !important;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

/* الشريط الجانبي المستقبلي */
.sidebar-cyber {
    background: var(--gradient-void);
    border-right: 2px solid var(--cyber-blue);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.sidebar-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: var(--gradient-cyber);
    animation: energyFlow 2s ease-in-out infinite alternate;
}

.sidebar-menu {
    list-style: none;
    padding: 2rem 0;
}

.sidebar-menu li {
    margin: 0.5rem 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    color: var(--cyber-blue);
    text-decoration: none;
    transition: all var(--animation-speed) ease;
    position: relative;
    overflow: hidden;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: rgba(0, 212, 255, 0.1);
    transition: width var(--animation-speed) ease;
}

.sidebar-menu a:hover::before,
.sidebar-menu a.active::before {
    width: 100%;
}

.sidebar-menu a:hover {
    color: var(--quantum-gold);
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    transform: translateX(-10px);
}

.sidebar-menu i {
    margin-left: 1rem;
    font-size: 1.2rem;
    width: 25px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    background: rgba(26, 26, 46, 0.3);
    min-height: 100vh;
    padding: 2rem;
    position: relative;
}

/* الجداول المستقبلية */
.table-cyber {
    background: rgba(26, 26, 46, 0.8);
    border-radius: var(--border-radius-card);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.table-cyber thead {
    background: var(--gradient-cyber);
}

.table-cyber thead th {
    color: var(--void-black);
    font-weight: 600;
    border: none;
    padding: 1.5rem 1rem;
    text-align: center;
}

.table-cyber tbody tr {
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    transition: all var(--animation-speed) ease;
}

.table-cyber tbody tr:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: scale(1.02);
}

.table-cyber tbody td {
    color: var(--cyber-blue);
    padding: 1rem;
    border: none;
    text-align: center;
}

/* النماذج المستقبلية */
.form-cyber {
    background: rgba(26, 26, 46, 0.8);
    border-radius: var(--border-radius-card);
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.form-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-cyber);
    animation: scanLine 3s ease-in-out infinite;
}

.form-control-cyber {
    background: rgba(10, 10, 15, 0.8);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: var(--border-radius-cyber);
    color: var(--cyber-blue);
    padding: 1rem 1.5rem;
    font-family: var(--font-tech);
    transition: all var(--animation-speed) ease;
}

.form-control-cyber:focus {
    border-color: var(--cyber-blue);
    box-shadow: var(--shadow-cyber);
    background: rgba(10, 10, 15, 0.9);
    outline: none;
}

.form-control-cyber::placeholder {
    color: rgba(0, 212, 255, 0.5);
}

.form-label-cyber {
    color: var(--cyber-blue);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-family: var(--font-tech);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* الإحصائيات المستقبلية */
.stat-card {
    background: var(--gradient-void);
    border-radius: var(--border-radius-card);
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 212, 255, 0.3);
    transition: all var(--animation-speed) ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0;
    transition: opacity var(--animation-speed) ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-cyber);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--cyber-blue);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--neon-purple);
    margin-bottom: 1rem;
    text-shadow: var(--shadow-neon);
}

/* الرسوم المتحركة المستقبلية */
@keyframes cosmicPulse {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes scanLine {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

@keyframes energyFlow {
    0% { box-shadow: 0 0 5px var(--cyber-blue); }
    100% { box-shadow: 0 0 20px var(--cyber-blue), 0 0 40px var(--cyber-blue); }
}

@keyframes hologramFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes dataStream {
    0% { transform: translateY(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateY(100%); opacity: 0; }
}

/* تأثيرات الظهور */
.fade-in-cyber {
    animation: fadeInCyber 0.8s ease-out forwards;
}

@keyframes fadeInCyber {
    from {
        opacity: 0;
        transform: translateY(30px) rotateX(10deg);
    }
    to {
        opacity: 1;
        transform: translateY(0) rotateX(0);
    }
}

/* تأثيرات التمرير */
.scroll-reveal-cyber {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.scroll-reveal-cyber.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* تصميم متجاوب مستقبلي */
@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    
    .btn-cyber {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
    
    .card-cyber .card-body {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .sidebar-cyber {
        transform: translateX(100%);
        transition: transform var(--animation-speed) ease;
        position: fixed;
        top: 0;
        right: 0;
        width: 280px;
        z-index: 1000;
    }
    
    .sidebar-cyber.active {
        transform: translateX(0);
    }
}

/* تأثيرات خاصة للنصوص */
.text-cyber {
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-neon {
    color: var(--neon-purple);
    text-shadow: 0 0 10px var(--neon-purple);
}

.text-plasma {
    color: var(--plasma-pink);
    text-shadow: 0 0 10px var(--plasma-pink);
}

.text-quantum {
    color: var(--quantum-gold);
    text-shadow: 0 0 10px var(--quantum-gold);
}

/* خلفيات متحركة للبطاقات */
.card-animated {
    position: relative;
    overflow: hidden;
}

.card-animated::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        var(--cyber-blue),
        transparent,
        var(--neon-purple),
        transparent,
        var(--plasma-pink),
        transparent
    );
    animation: rotate 4s linear infinite;
    z-index: -1;
}

.card-animated::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: var(--void-black);
    border-radius: inherit;
    z-index: -1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* لوحة التحكم الرئيسية */
.dashboard-header {
    background: var(--gradient-void);
    padding: 2rem;
    border-radius: var(--border-radius-card);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-cyber);
    animation: scanLine 4s ease-in-out infinite;
}

.dashboard-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--cyber-blue);
    font-size: 1.2rem;
    opacity: 0.8;
}

.dashboard-time {
    position: absolute;
    top: 2rem;
    left: 2rem;
    color: var(--quantum-gold);
    font-family: var(--font-mono);
    font-size: 1.1rem;
}

/* بطاقات الإحصائيات المتقدمة */
.advanced-stat-card {
    background: var(--gradient-void);
    border-radius: var(--border-radius-card);
    padding: 2rem;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 212, 255, 0.3);
    transition: all var(--animation-speed) ease;
    height: 200px;
}

.advanced-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0;
    transition: opacity var(--animation-speed) ease;
}

.advanced-stat-card:hover::before {
    opacity: 0.1;
}

.advanced-stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-cyber);
    border-color: var(--cyber-blue);
}

.stat-icon-advanced {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    font-size: 2.5rem;
    opacity: 0.3;
    transition: all var(--animation-speed) ease;
}

.advanced-stat-card:hover .stat-icon-advanced {
    opacity: 0.8;
    transform: scale(1.2) rotate(10deg);
}

.stat-value-advanced {
    font-size: 3rem;
    font-weight: 800;
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-label-advanced {
    color: var(--cyber-blue);
    font-weight: 600;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.stat-change {
    position: absolute;
    bottom: 1.5rem;
    left: 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--electric-green);
}

.stat-change.negative {
    color: var(--plasma-pink);
}

.stat-change i {
    margin-left: 0.3rem;
}

/* الرسوم البيانية المستقبلية */
.chart-container {
    background: rgba(26, 26, 46, 0.8);
    border-radius: var(--border-radius-card);
    padding: 2rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-cyber);
    animation: scanLine 3s ease-in-out infinite;
}

.chart-title {
    color: var(--cyber-blue);
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

/* قائمة الأنشطة الحديثة */
.activity-list {
    background: rgba(26, 26, 46, 0.8);
    border-radius: var(--border-radius-card);
    border: 1px solid rgba(0, 212, 255, 0.3);
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
    transition: all var(--animation-speed) ease;
    position: relative;
}

.activity-item:hover {
    background: rgba(0, 212, 255, 0.05);
    transform: translateX(-5px);
}

.activity-item::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--gradient-cyber);
    opacity: 0;
    transition: opacity var(--animation-speed) ease;
}

.activity-item:hover::before {
    opacity: 1;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-cyber);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--void-black);
    font-weight: bold;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: var(--cyber-blue);
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.activity-description {
    color: rgba(0, 212, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.activity-time {
    color: var(--quantum-gold);
    font-size: 0.8rem;
    font-family: var(--font-mono);
}

/* الإشعارات المستقبلية */
.notification-panel {
    position: fixed;
    top: 2rem;
    left: 2rem;
    width: 350px;
    z-index: 1000;
    pointer-events: none;
}

.notification-item {
    background: rgba(26, 26, 46, 0.95);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: var(--border-radius-cyber);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(20px);
    transform: translateX(-100%);
    transition: all 0.5s ease;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification-item.show {
    transform: translateX(0);
}

.notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-cyber);
}

.notification-item.success::before {
    background: var(--electric-green);
}

.notification-item.warning::before {
    background: var(--quantum-gold);
}

.notification-item.error::before {
    background: var(--plasma-pink);
}

.notification-close {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: none;
    border: none;
    color: var(--cyber-blue);
    cursor: pointer;
    font-size: 1.2rem;
    opacity: 0.7;
    transition: opacity var(--animation-speed) ease;
}

.notification-close:hover {
    opacity: 1;
}

/* أزرار الإجراءات السريعة */
.quick-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: var(--gradient-void);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: var(--border-radius-cyber);
    padding: 1rem 1.5rem;
    color: var(--cyber-blue);
    text-decoration: none;
    transition: all var(--animation-speed) ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.quick-action-btn:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--cyber-blue);
    color: var(--quantum-gold);
    transform: translateY(-3px);
    box-shadow: var(--shadow-cyber);
}

.quick-action-btn i {
    font-size: 1.2rem;
}

/* شريط التقدم المستقبلي */
.progress-cyber {
    background: rgba(10, 10, 15, 0.8);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.progress-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0.3;
}

.progress-bar-cyber {
    height: 100%;
    background: var(--gradient-cyber);
    border-radius: inherit;
    position: relative;
    overflow: hidden;
    transition: width 0.8s ease;
}

.progress-bar-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
