# 🎛️ دليل نظام إعدادات الموقع - IQHome

## 📋 نظرة عامة

تم إضافة نظام شامل لإدارة جميع إعدادات الموقع من لوحة التحكم الإدارية، مما يتيح للإدارة تغيير كل شيء في الموقع دون الحاجة لتعديل الكود.

## 🔧 الميزات المتاحة

### 1. **معلومات الشركة الأساسية**
- ✅ اسم الشركة (عربي/إنجليزي)
- ✅ شعار الشركة (عربي/إنجليزي)
- ✅ وصف الشركة ونشاطها

### 2. **معلومات التواصل**
- ✅ الهاتف الرئيسي والثانوي
- ✅ البريد الإلكتروني الرئيسي وبريد الدعم
- ✅ تحديث تلقائي في جميع صفحات الموقع

### 3. **معلومات العنوان**
- ✅ العنوان الرئيسي (عربي/إنجليزي)
- ✅ تفاصيل العنوان الكاملة
- ✅ ساعات العمل (عربي/إنجليزي)

### 4. **وسائل التواصل الاجتماعي**
- ✅ روابط فيسبوك، تويتر، إنستغرام
- ✅ رابط لينكد إن
- ✅ رقم واتساب

### 5. **إعدادات الموقع**
- ✅ عنوان الموقع (يظهر في المتصفح)
- ✅ وصف الموقع للـ SEO
- ✅ نص "من نحن" (عربي/إنجليزي)

### 6. **إعدادات الإحصائيات**
- ✅ تفعيل/إلغاء عرض الإحصائيات
- ✅ عدد المشاريع المنجزة
- ✅ سنوات الخبرة
- ✅ عدد العملاء

## 🚀 كيفية الاستخدام

### الوصول لصفحة الإعدادات:
1. **تسجيل الدخول**: http://127.0.0.1:8001/admin/login/
2. **الانتقال للإعدادات**: http://127.0.0.1:8001/admin/settings/
3. **أو من الشريط الجانبي**: النقر على "إعدادات الموقع"

### تحديث الإعدادات:
1. **تعديل الحقول المطلوبة**
2. **النقر على "حفظ الإعدادات"**
3. **التحديث فوري في جميع صفحات الموقع**

## 📱 الصفحات المتأثرة بالإعدادات

### الواجهة العامة:
- ✅ **الصفحة الرئيسية** (`/`)
  - اسم الشركة في شريط التنقل
  - العنوان الرئيسي والشعار
  - نص "من نحن"
  - الإحصائيات (إذا كانت مفعلة)
  - معلومات التواصل
  - التذييل

- ✅ **صفحة الخدمات** (`/services/`)
  - اسم الشركة في شريط التنقل
  - معلومات التواصل في التذييل

- ✅ **صفحة طلب الخدمة** (`/request-service/`)
  - اسم الشركة في شريط التنقل
  - معلومات التواصل الجانبية

- ✅ **صفحة التواصل** (`/contact/`)
  - اسم الشركة في شريط التنقل
  - جميع معلومات التواصل
  - العنوان وتفاصيله
  - ساعات العمل
  - وسائل التواصل الاجتماعي

### لوحة التحكم الإدارية:
- ✅ **جميع صفحات الإدارة**
  - اسم الشركة في شريط التنقل
  - معلومات التواصل

## 🔄 التحديث التلقائي

- **فوري**: التغييرات تظهر فوراً بعد الحفظ
- **تخزين مؤقت**: نظام cache ذكي لتحسين الأداء
- **مسح تلقائي**: مسح الـ cache عند التحديث

## 🛡️ الأمان والحماية

### الصلاحيات:
- ✅ **الإدارة فقط**: الوصول محدود للمديرين
- ✅ **حماية من الحذف**: لا يمكن حذف الإعدادات
- ✅ **إعدادات واحدة**: نسخة واحدة فقط من الإعدادات

### التحقق من البيانات:
- ✅ **الحقول المطلوبة**: التحقق من الحقول الأساسية
- ✅ **تنسيق البريد الإلكتروني**: التحقق من صحة الإيميل
- ✅ **تنسيق الروابط**: التحقق من صحة روابط وسائل التواصل

## 📊 الإحصائيات القابلة للتخصيص

### الإحصائيات المتاحة:
1. **عدد المشاريع**: يظهر كـ "X+ منزل ذكي"
2. **سنوات الخبرة**: يظهر كـ "X+ سنوات خبرة"
3. **عدد العملاء**: يظهر كـ "X+ عميل راضي"
4. **الدعم الفني**: ثابت "24/7 دعم فني"

### التحكم في العرض:
- ✅ **إظهار/إخفاء**: خيار لإظهار أو إخفاء قسم الإحصائيات
- ✅ **قيم مخصصة**: تحديد الأرقام حسب الواقع الفعلي

## 🎨 التكامل مع التصميم

### التحديث التلقائي:
- ✅ **جميع القوالب**: تحديث تلقائي في جميع الصفحات
- ✅ **التصميم الموحد**: الحفاظ على التصميم الاحترافي
- ✅ **التجاوب**: يعمل على جميع الأجهزة

### Context Processor:
- ✅ **متغيرات عامة**: متاحة في جميع القوالب
- ✅ **أداء محسن**: تخزين مؤقت ذكي
- ✅ **قيم افتراضية**: في حالة عدم وجود إعدادات

## 🔧 للمطورين

### الملفات المضافة:
```
company/models.py          # نموذج SiteSettings
company/context_processors.py  # معالج السياق
company/admin_views.py     # view إدارة الإعدادات
templates/admin/settings_dark.html  # صفحة الإعدادات
```

### الاستخدام في القوالب:
```django
{{ company_name }}         # اسم الشركة
{{ company_slogan }}       # شعار الشركة
{{ phone_primary }}        # الهاتف الرئيسي
{{ email_primary }}        # البريد الإلكتروني
{{ address_ar }}           # العنوان
{{ site_settings.about_us_ar }}  # نص من نحن
```

### إضافة حقول جديدة:
1. تحديث نموذج `SiteSettings`
2. إنشاء migration جديد
3. تحديث صفحة الإعدادات
4. تحديث context processor

## 🎯 الخلاصة

نظام إعدادات الموقع يوفر:
- ✅ **تحكم كامل** في محتوى الموقع
- ✅ **سهولة الاستخدام** للإدارة
- ✅ **تحديث فوري** بدون تعديل كود
- ✅ **أمان وحماية** للبيانات
- ✅ **أداء محسن** مع التخزين المؤقت

الآن يمكن للإدارة تغيير **كل شيء** في الموقع من لوحة التحكم! 🎉
