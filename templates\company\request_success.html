<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم إرسال طلبك بنجاح - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Arabic Luxury CSS -->
    <link href="/static/css/arabic-luxury.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-luxury">
        <div class="container">
            <a class="navbar-brand navbar-brand-luxury" href="/">
                <i class="fas fa-home me-2"></i>IQHome
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/services/">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-luxury" href="/#contact">تواصل معنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- صفحة النجاح -->
    <section class="py-5 min-vh-100 d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="text-center mb-5">
                        <!-- أيقونة النجاح -->
                        <div class="service-icon mx-auto mb-4" style="width: 120px; height: 120px; font-size: 4rem; background: linear-gradient(135deg, #28a745, #20c997);">
                            <i class="fas fa-check text-white"></i>
                        </div>
                        
                        <h1 class="text-gradient mb-3">تم إرسال طلبك بنجاح!</h1>
                        <p class="lead text-muted">
                            شكراً لك على ثقتك في IQHome. سيتواصل معك أحد خبرائنا خلال 24 ساعة
                        </p>
                    </div>

                    <!-- تفاصيل الطلب -->
                    <div class="card-luxury mb-4">
                        <div class="card-body p-4">
                            <h3 class="text-center mb-4">
                                <i class="fas fa-clipboard-list me-2 text-gradient"></i>
                                تفاصيل طلبك
                            </h3>
                            
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="border-end pe-4">
                                        <h5 class="text-gradient mb-3">معلومات الطلب</h5>
                                        <div class="mb-2">
                                            <strong>رقم الطلب:</strong>
                                            <span class="badge bg-primary fs-6 ms-2">{{ request.request_number }}</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>الخدمة المطلوبة:</strong>
                                            <div>{{ request.service.name_ar }}</div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>حالة الطلب:</strong>
                                            <span class="badge bg-warning text-dark">{{ request.get_status_display }}</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>تاريخ الطلب:</strong>
                                            <div>{{ request.created_at|date:"d/m/Y - H:i" }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5 class="text-gradient mb-3">معلومات العميل</h5>
                                    <div class="mb-2">
                                        <strong>الاسم:</strong>
                                        <div>{{ request.customer_name }}</div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>رقم الهاتف:</strong>
                                        <div>{{ request.customer_phone }}</div>
                                    </div>
                                    {% if request.customer_email %}
                                    <div class="mb-2">
                                        <strong>البريد الإلكتروني:</strong>
                                        <div>{{ request.customer_email }}</div>
                                    </div>
                                    {% endif %}
                                    <div class="mb-2">
                                        <strong>المدينة:</strong>
                                        <div>{{ request.customer_city }}</div>
                                    </div>
                                </div>
                            </div>
                            
                            {% if request.description %}
                            <hr>
                            <div>
                                <strong>وصف الطلب:</strong>
                                <div class="mt-2 p-3 bg-light rounded">{{ request.description }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الخطوات التالية -->
                    <div class="card-luxury mb-4">
                        <div class="card-body p-4">
                            <h3 class="text-center mb-4">
                                <i class="fas fa-route me-2 text-gradient"></i>
                                الخطوات التالية
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-4 text-center mb-3">
                                    <div class="service-icon mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <h5>1. التواصل</h5>
                                    <p class="text-muted">سيتصل بك أحد خبرائنا خلال 24 ساعة لمناقشة تفاصيل طلبك</p>
                                </div>
                                
                                <div class="col-md-4 text-center mb-3">
                                    <div class="service-icon mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <h5>2. تحديد الموعد</h5>
                                    <p class="text-muted">سنحدد موعد مناسب لزيارة مجانية لتقييم المشروع</p>
                                </div>
                                
                                <div class="col-md-4 text-center mb-3">
                                    <div class="service-icon mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                        <i class="fas fa-file-contract"></i>
                                    </div>
                                    <h5>3. العرض</h5>
                                    <p class="text-muted">سنقدم لك عرض سعر مفصل ومجاني مع خطة التنفيذ</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات مهمة -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-6">
                            <div class="card-luxury h-100">
                                <div class="card-body p-4 text-center">
                                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5 class="text-gradient">وقت الاستجابة</h5>
                                    <p class="mb-0">سنتواصل معك خلال <strong>24 ساعة</strong> كحد أقصى</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card-luxury h-100">
                                <div class="card-body p-4 text-center">
                                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                    <h5 class="text-gradient">استشارة مجانية</h5>
                                    <p class="mb-0">الاستشارة والزيارة الأولى <strong>مجانية تماماً</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار العمل -->
                    <div class="text-center">
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="/" class="btn btn-luxury">
                                <i class="fas fa-home me-2"></i>العودة للرئيسية
                            </a>
                            <a href="/services/" class="btn btn-elegant">
                                <i class="fas fa-list me-2"></i>تصفح خدماتنا
                            </a>
                            <a href="tel:+9647701234567" class="btn btn-success">
                                <i class="fas fa-phone me-2"></i>اتصل بنا الآن
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <p class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                احتفظ برقم طلبك: <strong>{{ request.request_number }}</strong> للمراجعة
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- معلومات التواصل السريع -->
    <section class="py-4 bg-pattern">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h4 class="mb-3">هل تحتاج مساعدة فورية؟</h4>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="tel:+9647701234567" class="btn btn-outline-primary">
                            <i class="fas fa-phone me-2"></i>+964 ************
                        </a>
                        <a href="https://wa.me/9647701234567" class="btn btn-outline-success">
                            <i class="fab fa-whatsapp me-2"></i>واتساب
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-info">
                            <i class="fas fa-envelope me-2"></i><EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer-luxury">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2025 IQHome. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تأثير الاحتفال بالنجاح
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير الظهور التدريجي
            const elements = document.querySelectorAll('.card-luxury, .service-icon');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
            
            // تأثير النبض على أيقونة النجاح
            const successIcon = document.querySelector('.fa-check').parentElement;
            setInterval(() => {
                successIcon.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    successIcon.style.transform = 'scale(1)';
                }, 200);
            }, 3000);
        });

        // نسخ رقم الطلب عند النقر
        document.addEventListener('click', function(e) {
            if (e.target.textContent.includes('{{ request.request_number }}')) {
                navigator.clipboard.writeText('{{ request.request_number }}').then(() => {
                    // إظهار رسالة نجاح النسخ
                    const toast = document.createElement('div');
                    toast.className = 'alert alert-success position-fixed top-0 start-50 translate-middle-x mt-3';
                    toast.style.zIndex = '9999';
                    toast.textContent = 'تم نسخ رقم الطلب!';
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.remove();
                    }, 2000);
                });
            }
        });
    </script>
</body>
</html>
