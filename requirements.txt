# IQHome Smart Home Platform Requirements

# Core Django
Django>=5.2.4
djangorestframework>=3.16.0
djangorestframework-simplejwt>=5.5.1

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter

# CORS handling
django-cors-headers>=4.7.0

# Environment variables
python-decouple>=3.8

# API Documentation
drf-yasg>=1.21.10

# Image handling
Pillow>=11.0.0

# Development dependencies
django-debug-toolbar>=4.0.0  # For development debugging

# Testing
pytest>=8.0.0
pytest-django>=4.8.0
factory-boy>=3.3.0

# Code quality
black>=24.0.0
flake8>=7.0.0
isort>=5.13.0

# Production dependencies
gunicorn>=21.2.0  # WSGI server for production
whitenoise>=6.6.0  # Static file serving
redis>=5.0.0  # Caching and session storage
celery>=5.3.0  # Background task processing

# Monitoring and logging
sentry-sdk>=1.38.0  # Error tracking
django-extensions>=3.2.0  # Additional Django commands

# Security
django-ratelimit>=4.1.0  # Rate limiting
django-security>=0.17.0  # Security middleware
