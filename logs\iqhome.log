Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\devices\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\support\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\notifications\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3785
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3836
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3785
"GET /login/ HTTP/1.1" 200 7157
"GET /admin/login/ HTTP/1.1" 200 4160
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /register/ HTTP/1.1" 200 8935
"POST /register/ HTTP/1.1" 302 0
"GET /login/ HTTP/1.1" 200 7493
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /login/ HTTP/1.1" 302 0
Not Found: /dashboard/
"GET /dashboard/ HTTP/1.1" 404 3833
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /swagger/ HTTP/1.1" 200 1606
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 200 1046583
"GET /static/drf-yasg/style.css HTTP/1.1" 200 1047
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui.css HTTP/1.1" 200 145206
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 200 322863
"GET /static/drf-yasg/insQ.min.js HTTP/1.1" 200 2093
"GET /static/drf-yasg/swagger-ui-init.js HTTP/1.1" 200 15480
"GET /static/drf-yasg/immutable.min.js HTTP/1.1" 200 56904
"GET /swagger/?format=openapi HTTP/1.1" 200 42485
"GET /static/drf-yasg/swagger-ui-dist/favicon-32x32.png HTTP/1.1" 200 628
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/dashboard/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/dashboard/ HTTP/1.1" 200 3715
"GET /swagger/ HTTP/1.1" 200 1606
"GET /static/drf-yasg/style.css HTTP/1.1" 304 0
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui.css HTTP/1.1" 304 0
"GET /static/drf-yasg/swagger-ui-init.js HTTP/1.1" 304 0
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui-standalone-preset.js HTTP/1.1" 304 0
"GET /static/drf-yasg/swagger-ui-dist/swagger-ui-bundle.js HTTP/1.1" 304 0
"GET /static/drf-yasg/insQ.min.js HTTP/1.1" 304 0
"GET /static/drf-yasg/immutable.min.js HTTP/1.1" 304 0
"GET /swagger/?format=openapi HTTP/1.1" 200 42733
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\serializers.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14495
"GET /static/css/iqhome.css HTTP/1.1" 200 8030
"GET /static/js/iqhome.js HTTP/1.1" 200 15024
Not Found: /devices/
"GET /devices/ HTTP/1.1" 404 8068
"GET /dashboard/ HTTP/1.1" 200 20775
"GET /static/js/iqhome.js HTTP/1.1" 304 0
"GET /api/devices/ HTTP/1.1" 200 1420
"GET /api/notifications/ HTTP/1.1" 200 3578
Not Found: /devices/
"GET /devices/ HTTP/1.1" 404 8068
"GET /profile/ HTTP/1.1" 200 11330
"GET /static/css/iqhome.css HTTP/1.1" 304 0
Not Found: /devices/
"GET /devices/ HTTP/1.1" 404 8068
"GET /profile/ HTTP/1.1" 200 11330
"GET /static/js/iqhome.js HTTP/1.1" 304 0
Not Found: /devices/
"GET /devices/ HTTP/1.1" 404 8068
"GET / HTTP/1.1" 200 14495
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 26271
"GET /static/css/arabic-luxury.css HTTP/1.1" 200 10083
"GET / HTTP/1.1" 200 26271
"GET /services/ HTTP/1.1" 200 26716
"GET /static/css/arabic-luxury.css HTTP/1.1" 304 0
"GET /request-service/?service=29e53f65-47a2-4bf5-870f-56b313f1fa3c HTTP/1.1" 200 21347
"GET / HTTP/1.1" 200 26271
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/login/ HTTP/1.1" 200 18725
"GET /static/css/admin-futuristic.css HTTP/1.1" 200 24248
"POST /admin/login/ HTTP/1.1" 200 18949
"GET /static/css/admin-futuristic.css HTTP/1.1" 304 0
"POST /admin/login/ HTTP/1.1" 200 18949
"GET / HTTP/1.1" 200 26271
"GET /static/css/arabic-luxury.css HTTP/1.1" 304 0
"GET /admin/login/ HTTP/1.1" 200 18725
"GET /static/css/admin-futuristic.css HTTP/1.1" 304 0
"POST /admin/login/ HTTP/1.1" 302 0
Not Found: /accounts/profile/
"GET /accounts/profile/ HTTP/1.1" 404 13150
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 14821
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
Not Found: /admin/analytics/
"GET /admin/analytics/ HTTP/1.1" 404 8080
Not Found: /accounts/profile/
"GET /accounts/profile/ HTTP/1.1" 404 13150
"GET /login/ HTTP/1.1" 200 8030
"GET /static/js/iqhome.js HTTP/1.1" 304 0
"GET /static/css/iqhome.css HTTP/1.1" 304 0
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\accounts\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 15222
"GET / HTTP/1.1" 200 15222
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
Not Found: /login/
"GET /login/ HTTP/1.1" 404 6376
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
"POST /admin/logout/ HTTP/1.1" 200 3568
"GET /admin/login/ HTTP/1.1" 200 17723
"GET /static/css/simple-admin.css HTTP/1.1" 200 7411
"POST /admin/login/ HTTP/1.1" 302 0
Not Found: /accounts/profile/
"GET /accounts/profile/ HTTP/1.1" 404 6409
"GET / HTTP/1.1" 200 13483
"GET /static/css/dark-professional.css HTTP/1.1" 200 16266
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
"GET /request-service/ HTTP/1.1" 200 21339
"GET /static/css/arabic-luxury.css HTTP/1.1" 304 0
"GET /services/ HTTP/1.1" 200 26716
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13040
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 200 12124
"GET /services/ HTTP/1.1" 200 24742
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /request-service/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 107, in request_service_view
    return render(request, 'company/request_service_dark.html', {'services': services})
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' == 'success' and 'check-circle' or 'exclamation-triangle'' from 'message.tags == 'success' and 'check-circle' or 'exclamation-triangle''
"GET /request-service/ HTTP/1.1" **********
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 9000
Not Found: /admin/dashboard/
"GET /admin/dashboard/ HTTP/1.1" 404 6225
Not Found: /admin/services/
"GET /admin/services/ HTTP/1.1" 404 6222
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /admin/messages/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 275, in admin_messages_view
    return render(request, 'admin/messages.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/messages.html
"GET /admin/messages/ HTTP/1.1" 500 98701
"GET /admin/dashboard/ HTTP/1.1" 200 11515
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/dashboard/ HTTP/1.1" 200 11515
Internal Server Error: /admin/requests/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 208, in admin_requests_view
    return render(request, 'admin/requests.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/requests.html
"GET /admin/requests/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13040
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/services/ HTTP/1.1" 200 41383
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
Internal Server Error: /admin/requests/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 214, in admin_requests_view
    return render(request, 'admin/requests.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/requests.html
"GET /admin/requests/ HTTP/1.1" **********
Internal Server Error: /admin/messages/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 281, in admin_messages_view
    return render(request, 'admin/messages.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/messages.html
"GET /admin/messages/ HTTP/1.1" 500 98867
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/requests/ HTTP/1.1" 200 16115
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
Internal Server Error: /admin/messages/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 281, in admin_messages_view
    return render(request, 'admin/messages.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/messages.html
"GET /admin/messages/ HTTP/1.1" 500 98867
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/messages/ HTTP/1.1" 200 20391
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 13040
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /contact/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 154, in contact_view
    return render(request, 'company/contact_dark.html')
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' == 'success' and 'check-circle' or 'exclamation-triangle'' from 'message.tags == 'success' and 'check-circle' or 'exclamation-triangle''
"GET /contact/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13040
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
Internal Server Error: /request-service/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 107, in request_service_view
    return render(request, 'company/request_service_dark.html', {'services': services})
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' == 'success' and 'check-circle' or 'exclamation-triangle'' from 'message.tags == 'success' and 'check-circle' or 'exclamation-triangle''
"GET /request-service/ HTTP/1.1" **********
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\config\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /admin/settings/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 312, in admin_settings_view
    return render(request, 'admin/settings_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' == 'success' and 'check-circle' or 'exclamation-triangle'' from 'message.tags == 'success' and 'check-circle' or 'exclamation-triangle''
"GET /admin/settings/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13076
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /static/css/dark-professional.css HTTP/1.1" 200 16463
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13096
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
Internal Server Error: /admin/settings/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 312, in admin_settings_view
    return render(request, 'admin/settings_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "D:\samir\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' == 'success' and 'check-circle' or 'exclamation-triangle'' from 'message.tags == 'success' and 'check-circle' or 'exclamation-triangle''
"GET /admin/settings/ HTTP/1.1" **********
"GET /admin/settings/ HTTP/1.1" 200 24844
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /request-service/ HTTP/1.1" 200 20497
"GET /contact/ HTTP/1.1" 200 22734
"GET / HTTP/1.1" 200 13096
"GET /request-service/ HTTP/1.1" 200 20497
"GET / HTTP/1.1" 200 13096
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/dashboard/ HTTP/1.1" 200 11751
"GET /admin/settings/ HTTP/1.1" 200 24844
"GET /admin/dashboard/ HTTP/1.1" 200 11751
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 13096
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13262
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /static/css/dark-professional.css HTTP/1.1" 200 18232
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13262
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" **********
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" **********
Not Found: /admin/company/portfolio/
"GET /admin/company/portfolio/ HTTP/1.1" 404 8082
Not Found: /admin/company/portfolio/
"GET /admin/company/portfolio/ HTTP/1.1" 404 8082
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" 500 169569
Internal Server Error: /admin/portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 349, in admin_portfolio_view
    return render(request, 'admin/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /admin/portfolio/ HTTP/1.1" 500 173011
"GET /admin/testimonials/ HTTP/1.1" 200 19136
Internal Server Error: /admin/content/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 423, in admin_content_view
    return render(request, 'admin/content_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/content_dark.html
"GET /admin/content/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13262
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" **********
Internal Server Error: /portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 890, in _resolve_lookup
    raise TypeError
TypeError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 177, in portfolio_view
    return render(request, 'company/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 901, in _resolve_lookup
    current = getattr(current, bit)
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 69, in url
    self._require_file()
    ~~~~~~~~~~~~~~~~~~^^
  File "D:\samir\venv\Lib\site-packages\django\db\models\fields\files.py", line 44, in _require_file
    raise ValueError(
        "The '%s' attribute has no file associated with it." % self.field.name
    )
ValueError: The 'featured_image' attribute has no file associated with it.
"GET /portfolio/ HTTP/1.1" **********
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /portfolio/ HTTP/1.1" 200 29571
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /portfolio/ HTTP/1.1" 200 30283
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 200 13547
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 200 13834
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 200 13239
Internal Server Error: /admin/portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 349, in admin_portfolio_view
    return render(request, 'admin/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "D:\samir\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'portfolio_detail' with arguments '(3,)' not found. 1 pattern(s) tried: ['portfolio/(?P<project_id>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\\Z']
"GET /admin/portfolio/ HTTP/1.1" **********
Not Found: /admin/company/portfolio/
"GET /admin/company/portfolio/ HTTP/1.1" 404 8082
"GET / HTTP/1.1" 200 13262
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /portfolio/ HTTP/1.1" 200 30283
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 13262
"GET /admin/dashboard/ HTTP/1.1" 200 12483
Internal Server Error: /admin/content/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 423, in admin_content_view
    return render(request, 'admin/content_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/content_dark.html
"GET /admin/content/ HTTP/1.1" **********
"GET /admin/content/ HTTP/1.1" 200 18864
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /admin/dashboard/ HTTP/1.1" 200 12483
Not Found: /admin/company/faq/
"GET /admin/company/faq/ HTTP/1.1" 404 8064
Not Found: /admin/company/team/
"GET /admin/company/team/ HTTP/1.1" 404 8067
Not Found: /admin/company/partner/
"GET /admin/company/partner/ HTTP/1.1" 404 8076
"GET /admin/content/ HTTP/1.1" 200 22512
"GET /admin/testimonials/ HTTP/1.1" 200 27054
"GET /admin/dashboard/ HTTP/1.1" 200 12483
"GET / HTTP/1.1" 200 13262
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
Internal Server Error: /admin/portfolio/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 349, in admin_portfolio_view
    return render(request, 'admin/portfolio_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\samir\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "D:\samir\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'portfolio_detail' with arguments '(3,)' not found. 1 pattern(s) tried: ['portfolio/(?P<project_id>[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/\\Z']
"GET /admin/portfolio/ HTTP/1.1" **********
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/portfolio/ HTTP/1.1" 200 27595
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 304 0
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
Not Found: /admin/company/portfolio/2/change/
"GET /admin/company/portfolio/2/change/ HTTP/1.1" 404 8108
Internal Server Error: /portfolio/2/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 184, in portfolio_detail_view
    project = get_object_or_404(Portfolio, id=project_id, is_published=True)
              ^^^^^^^^^^^^^^^^^
NameError: name 'get_object_or_404' is not defined
"GET /portfolio/2/ HTTP/1.1" 500 74908
"GET /admin/portfolio/ HTTP/1.1" 200 27595
"GET /portfolio/ HTTP/1.1" 200 30283
"GET /admin/testimonials/ HTTP/1.1" 200 19136
"GET /admin/dashboard/ HTTP/1.1" 200 12483
"GET /admin/portfolio/ HTTP/1.1" 200 27595
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /admin/portfolio/ HTTP/1.1" 200 27595
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/portfolio/add/ HTTP/1.1" 200 23459
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /admin/portfolio/ HTTP/1.1" 200 30737
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_2.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
"POST /admin/portfolio/delete/2/ HTTP/1.1" 302 0
"GET /admin/portfolio/ HTTP/1.1" 200 26842
Internal Server Error: /portfolio/3/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 184, in portfolio_detail_view
    project = get_object_or_404(Portfolio, id=project_id, is_published=True)
              ^^^^^^^^^^^^^^^^^
NameError: name 'get_object_or_404' is not defined
"GET /portfolio/3/ HTTP/1.1" 500 74952
"GET / HTTP/1.1" 200 13262
"GET /contact/ HTTP/1.1" 200 23217
"GET /admin/portfolio/ HTTP/1.1" 200 26842
Internal Server Error: /portfolio/3/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 184, in portfolio_detail_view
    project = get_object_or_404(Portfolio, id=project_id, is_published=True)
              ^^^^^^^^^^^^^^^^^
NameError: name 'get_object_or_404' is not defined
"GET /portfolio/3/ HTTP/1.1" 500 74598
"GET /admin/testimonials/add/ HTTP/1.1" 200 18831
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /portfolio/1/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 195, in portfolio_detail_view
    return render(request, 'company/portfolio_detail.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: company/portfolio_detail.html
"GET /portfolio/1/ HTTP/1.1" 500 91301
"GET /contact/ HTTP/1.1" 200 22734
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /admin/portfolio/ HTTP/1.1" 200 26922
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
"GET /admin/portfolio/ HTTP/1.1" 200 26922
"GET /admin/portfolio/add/ HTTP/1.1" 200 23459
Internal Server Error: /admin/portfolio/edit/1/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\samir\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\admin_views.py", line 538, in admin_portfolio_edit_view
    return render(request, 'admin/portfolio_edit_dark.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: admin/portfolio_edit_dark.html
"GET /admin/portfolio/edit/1/ HTTP/1.1" **********
Internal Server Error: /portfolio/1/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 195, in portfolio_detail_view
    return render(request, 'company/portfolio_detail.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: company/portfolio_detail.html
"GET /portfolio/1/ HTTP/1.1" 500 91301
"GET /admin/testimonials/ HTTP/1.1" 200 20305
"GET /admin/testimonials/add/ HTTP/1.1" 200 18831
"GET /admin/services/ HTTP/1.1" 200 41337
"GET /admin/services/add/ HTTP/1.1" 200 14069
"GET /portfolio/ HTTP/1.1" 200 26014
"GET /media/portfolio/images/project_3.jpg HTTP/1.1" 304 0
"GET /static/css/dark-professional.css HTTP/1.1" 304 0
"GET /media/portfolio/images/project_1.jpg HTTP/1.1" 304 0
"GET /static/js/dark-professional.js HTTP/1.1" 304 0
"GET /admin/portfolio/ HTTP/1.1" 200 26922
Internal Server Error: /portfolio/3/
Traceback (most recent call last):
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\samir\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Vs Project\HS TEAM\SMART HOME\company\views.py", line 195, in portfolio_detail_view
    return render(request, 'company/portfolio_detail.html', context)
  File "D:\samir\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\samir\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: company/portfolio_detail.html
"GET /portfolio/3/ HTTP/1.1" 500 91464
