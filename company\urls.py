from django.urls import path
from . import views, admin_views

app_name = 'company'

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.home_view, name='home'),

    # صفحات الشركة البسيطة
    path('services/', views.services_view, name='services'),
    path('services/<uuid:service_id>/', views.service_detail_view, name='service_detail'),
    path('request-service/', views.request_service_view, name='request_service'),
    path('request-success/<uuid:request_id>/', views.request_success_view, name='request_success'),
    path('contact/', views.contact_view, name='contact'),
    path('portfolio/', views.portfolio_view, name='portfolio'),
    path('portfolio/<int:project_id>/', views.portfolio_detail_view, name='portfolio_detail'),

    # لوحة التحكم الإدارية البسيطة
    path('admin/login/', admin_views.admin_login_view, name='admin_login'),
    path('admin/logout/', admin_views.admin_logout_view, name='admin_logout'),
    path('admin/dashboard/', admin_views.admin_dashboard_view, name='admin_dashboard'),
    path('admin/services/', admin_views.admin_services_view, name='admin_services'),
    path('admin/services/<uuid:service_id>/edit/', admin_views.admin_service_edit_view, name='admin_service_edit'),
    path('admin/requests/', admin_views.admin_requests_view, name='admin_requests'),
    path('admin/requests/<uuid:request_id>/', admin_views.admin_request_detail_view, name='admin_request_detail'),
    path('admin/messages/', admin_views.admin_messages_view, name='admin_messages'),
    path('admin/settings/', admin_views.admin_settings_view, name='admin_settings'),
    path('admin/portfolio/', admin_views.admin_portfolio_view, name='admin_portfolio'),
    path('admin/testimonials/', admin_views.admin_testimonials_view, name='admin_testimonials'),
    path('admin/content/', admin_views.admin_content_view, name='admin_content'),

    # Portfolio CRUD URLs
    path('admin/portfolio/add/', admin_views.admin_portfolio_add_view, name='admin_portfolio_add'),
    path('admin/portfolio/edit/<int:portfolio_id>/', admin_views.admin_portfolio_edit_view, name='admin_portfolio_edit'),
    path('admin/portfolio/delete/<int:portfolio_id>/', admin_views.admin_portfolio_delete_view, name='admin_portfolio_delete'),
    path('admin/portfolio/toggle/<int:portfolio_id>/', admin_views.admin_portfolio_toggle_status_view, name='admin_portfolio_toggle'),

    # Testimonials CRUD URLs
    path('admin/testimonials/add/', admin_views.admin_testimonial_add_view, name='admin_testimonial_add'),
    path('admin/testimonials/edit/<int:testimonial_id>/', admin_views.admin_testimonial_edit_view, name='admin_testimonial_edit'),
    path('admin/testimonials/delete/<int:testimonial_id>/', admin_views.admin_testimonial_delete_view, name='admin_testimonial_delete'),

    # Services CRUD URLs
    path('admin/services/add/', admin_views.admin_service_add_view, name='admin_service_add'),
    path('admin/services/edit/<uuid:service_id>/', admin_views.admin_service_edit_view, name='admin_service_edit'),
    path('admin/services/delete/<uuid:service_id>/', admin_views.admin_service_delete_view, name='admin_service_delete'),
    path('admin/services/toggle/<uuid:service_id>/', admin_views.admin_service_toggle_status_view, name='admin_service_toggle'),
]
