<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرسائل - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>IQHome - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}" class="active">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-envelope me-2"></i>إدارة رسائل التواصل
                            </h1>
                            <p class="text-secondary-dark">عرض والرد على رسائل العملاء</p>
                        </div>
                        <div>
                            <button class="btn-outline-dark" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i>تحديد الكل كمقروء
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-number-dark">{{ total_messages }}</div>
                            <div class="stat-label-dark">إجمالي الرسائل</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div class="stat-number-dark">{{ unread_messages }}</div>
                            <div class="stat-label-dark">رسائل غير مقروءة</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-number-dark">{{ today_messages }}</div>
                            <div class="stat-label-dark">رسائل اليوم</div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card-dark">
                            <div class="stat-icon-dark">
                                <i class="fas fa-reply"></i>
                            </div>
                            <div class="stat-number-dark">{{ replied_messages }}</div>
                            <div class="stat-label-dark">تم الرد عليها</div>
                        </div>
                    </div>
                </div>

                <!-- فلترة وبحث -->
                <div class="card-dark-pro mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-group-dark">
                                <label class="form-label-dark">البحث في الرسائل</label>
                                <input type="text" class="form-control-dark" id="searchMessages" placeholder="ابحث بالاسم أو الموضوع...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">الحالة</label>
                                <select class="form-control-dark" id="filterStatus">
                                    <option value="">جميع الرسائل</option>
                                    <option value="unread">غير مقروءة</option>
                                    <option value="read">مقروءة</option>
                                    <option value="replied">تم الرد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">التاريخ</label>
                                <input type="date" class="form-control-dark" id="filterDate">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">الترتيب</label>
                                <select class="form-control-dark" id="sortOrder">
                                    <option value="newest">الأحدث أولاً</option>
                                    <option value="oldest">الأقدم أولاً</option>
                                    <option value="unread">غير المقروءة أولاً</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group-dark">
                                <label class="form-label-dark">&nbsp;</label>
                                <button class="btn-outline-dark w-100" onclick="clearFilters()">
                                    <i class="fas fa-times"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الرسائل -->
                <div class="row g-4" id="messagesContainer">
                    {% for message in messages %}
                    <div class="col-12 message-item" data-status="{% if not message.is_read %}unread{% elif message.replied_at %}replied{% else %}read{% endif %}" data-date="{{ message.created_at|date:'Y-m-d' }}">
                        <div class="card-dark-pro {% if not message.is_read %}border-primary{% endif %}" style="{% if not message.is_read %}border-width: 2px;{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="service-icon-dark me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h5 class="text-primary-dark mb-1">{{ message.name }}</h5>
                                            <div class="d-flex align-items-center gap-3">
                                                <small class="text-secondary-dark">
                                                    <i class="fas fa-envelope me-1"></i>{{ message.email }}
                                                </small>
                                                {% if message.phone %}
                                                <small class="text-secondary-dark">
                                                    <i class="fas fa-phone me-1"></i>{{ message.phone }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <h6 class="text-accent mb-2">{{ message.subject }}</h6>
                                    <p class="text-secondary-dark mb-3">{{ message.message|truncatewords:30 }}</p>
                                    
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center gap-3">
                                            <small class="text-muted-dark">
                                                <i class="fas fa-clock me-1"></i>{{ message.created_at|timesince }}
                                            </small>
                                            {% if not message.is_read %}
                                                <span class="badge-danger">جديد</span>
                                            {% elif message.replied_at %}
                                                <span class="badge-success">تم الرد</span>
                                            {% else %}
                                                <span class="badge-secondary">مقروء</span>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button class="btn-outline-dark btn-sm" onclick="viewMessage('{{ message.id }}')" title="عرض كامل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if not message.is_read %}
                                            <button class="btn-outline-dark btn-sm" onclick="markAsRead('{{ message.id }}')" title="تحديد كمقروء">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn-dark-pro btn-sm" onclick="replyToMessage('{{ message.id }}')" title="رد">
                                                <i class="fas fa-reply"></i>
                                            </button>
                                            <button class="btn-outline-dark btn-sm" onclick="contactSender('{{ message.email }}', '{{ message.phone }}')" title="تواصل">
                                                <i class="fas fa-phone"></i>
                                            </button>
                                            <button class="btn-outline-dark btn-sm text-danger" onclick="deleteMessage('{{ message.id }}')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="card-dark-pro text-center">
                            <div class="py-5">
                                <i class="fas fa-inbox fa-3x text-muted-dark mb-3"></i>
                                <h4 class="text-secondary-dark">لا توجد رسائل بعد</h4>
                                <p class="text-muted-dark">ستظهر رسائل العملاء هنا عند وصولها</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if messages.has_other_pages %}
                <div class="d-flex justify-content-center mt-4">
                    <nav>
                        <ul class="pagination">
                            {% if messages.has_previous %}
                                <li class="page-item">
                                    <a class="page-link bg-dark text-light border-secondary" href="?page={{ messages.previous_page_number }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for num in messages.paginator.page_range %}
                                {% if messages.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link bg-primary border-primary">{{ num }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link bg-dark text-light border-secondary" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if messages.has_next %}
                                <li class="page-item">
                                    <a class="page-link bg-dark text-light border-secondary" href="?page={{ messages.next_page_number }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal عرض الرسالة كاملة -->
    <div class="modal fade" id="viewMessageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--charcoal-medium); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title text-primary-dark">
                        <i class="fas fa-envelope me-2"></i>تفاصيل الرسالة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body" id="messageDetails">
                    <!-- سيتم تحميل تفاصيل الرسالة هنا -->
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn-secondary-dark" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn-dark-pro" onclick="replyFromModal()">
                        <i class="fas fa-reply"></i>رد على الرسالة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal الرد على الرسالة -->
    <div class="modal fade" id="replyMessageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: var(--charcoal-medium); border: 1px solid var(--border-color);">
                <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title text-primary-dark">
                        <i class="fas fa-reply me-2"></i>الرد على الرسالة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <form id="replyForm">
                        <div class="form-group-dark">
                            <label class="form-label-dark">إلى:</label>
                            <input type="email" class="form-control-dark" id="replyTo" readonly>
                        </div>
                        <div class="form-group-dark">
                            <label class="form-label-dark">الموضوع:</label>
                            <input type="text" class="form-control-dark" id="replySubject">
                        </div>
                        <div class="form-group-dark">
                            <label class="form-label-dark">الرسالة:</label>
                            <textarea class="form-control-dark" id="replyMessage" rows="6" placeholder="اكتب ردك هنا..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn-secondary-dark" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn-dark-pro" onclick="sendReply()">
                        <i class="fas fa-paper-plane"></i>إرسال الرد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript خاص بإدارة الرسائل -->
    <script>
        let currentMessageId = null;
        
        // البحث والفلترة
        document.getElementById('searchMessages').addEventListener('input', filterMessages);
        document.getElementById('filterStatus').addEventListener('change', filterMessages);
        document.getElementById('filterDate').addEventListener('change', filterMessages);
        document.getElementById('sortOrder').addEventListener('change', sortMessages);
        
        function filterMessages() {
            const searchTerm = document.getElementById('searchMessages').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const dateFilter = document.getElementById('filterDate').value;
            const items = document.querySelectorAll('.message-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                const status = item.dataset.status;
                const date = item.dataset.date;
                
                const matchesSearch = text.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;
                const matchesDate = !dateFilter || date === dateFilter;
                
                if (matchesSearch && matchesStatus && matchesDate) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        function sortMessages() {
            const sortOrder = document.getElementById('sortOrder').value;
            const container = document.getElementById('messagesContainer');
            const items = Array.from(container.querySelectorAll('.message-item'));
            
            items.sort((a, b) => {
                const dateA = new Date(a.dataset.date);
                const dateB = new Date(b.dataset.date);
                
                if (sortOrder === 'oldest') {
                    return dateA - dateB;
                } else if (sortOrder === 'unread') {
                    const statusA = a.dataset.status;
                    const statusB = b.dataset.status;
                    if (statusA === 'unread' && statusB !== 'unread') return -1;
                    if (statusA !== 'unread' && statusB === 'unread') return 1;
                    return dateB - dateA;
                } else {
                    return dateB - dateA;
                }
            });
            
            items.forEach(item => container.appendChild(item));
        }
        
        function clearFilters() {
            document.getElementById('searchMessages').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterDate').value = '';
            document.getElementById('sortOrder').value = 'newest';
            filterMessages();
        }
        
        // وظائف إدارة الرسائل
        function viewMessage(messageId) {
            // TODO: تحميل تفاصيل الرسالة
            document.getElementById('messageDetails').innerHTML = '<p class="text-center">جاري تحميل تفاصيل الرسالة...</p>';
            const modal = new bootstrap.Modal(document.getElementById('viewMessageModal'));
            modal.show();
            
            // محاكاة تحميل البيانات
            setTimeout(() => {
                document.getElementById('messageDetails').innerHTML = `
                    <div class="alert-dark alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تنفيذ عرض تفاصيل الرسالة قريباً
                    </div>
                `;
            }, 1000);
        }
        
        function markAsRead(messageId) {
            // TODO: تحديد الرسالة كمقروءة
            showNotification('سيتم تحديد الرسالة كمقروءة قريباً', 'info');
        }
        
        function markAllAsRead() {
            if (confirm('هل تريد تحديد جميع الرسائل كمقروءة؟')) {
                // TODO: تحديد جميع الرسائل كمقروءة
                showNotification('سيتم تحديد جميع الرسائل كمقروءة قريباً', 'info');
            }
        }
        
        function replyToMessage(messageId) {
            currentMessageId = messageId;
            // TODO: تحميل بيانات الرسالة للرد
            document.getElementById('replyTo').value = '<EMAIL>';
            document.getElementById('replySubject').value = 'Re: استفسار عن الخدمات';
            
            const modal = new bootstrap.Modal(document.getElementById('replyMessageModal'));
            modal.show();
        }
        
        function replyFromModal() {
            if (currentMessageId) {
                replyToMessage(currentMessageId);
            }
            bootstrap.Modal.getInstance(document.getElementById('viewMessageModal')).hide();
        }
        
        function sendReply() {
            const replyTo = document.getElementById('replyTo').value;
            const subject = document.getElementById('replySubject').value;
            const message = document.getElementById('replyMessage').value;
            
            if (!message.trim()) {
                showNotification('يرجى كتابة نص الرد', 'warning');
                return;
            }
            
            // TODO: إرسال الرد
            showNotification('سيتم إرسال الرد قريباً', 'info');
            bootstrap.Modal.getInstance(document.getElementById('replyMessageModal')).hide();
        }
        
        function contactSender(email, phone) {
            const options = [];
            if (email) options.push(`البريد الإلكتروني: ${email}`);
            if (phone) options.push(`الهاتف: ${phone}`);
            
            if (options.length > 0) {
                alert('معلومات التواصل:\n' + options.join('\n'));
            }
        }
        
        function deleteMessage(messageId) {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                // TODO: حذف الرسالة
                showNotification('سيتم حذف الرسالة قريباً', 'warning');
            }
        }
        
        // تحديث تلقائي للرسائل كل دقيقة
        setInterval(function() {
            // TODO: تحديث الرسائل تلقائياً
        }, 60000);
    </script>
</body>
</html>
