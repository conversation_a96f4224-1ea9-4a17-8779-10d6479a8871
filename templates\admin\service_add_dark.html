<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة خدمة جديدة - {{ company_name }}</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>{{ company_name }} - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}" class="active">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_portfolio' %}">
                            <i class="fas fa-images"></i>
                            معرض الأعمال
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_testimonials' %}">
                            <i class="fas fa-star"></i>
                            آراء العملاء
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_content' %}">
                            <i class="fas fa-file-alt"></i>
                            إدارة المحتوى
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}">
                            <i class="fas fa-cog"></i>
                            إعدادات الموقع
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-plus me-2"></i>إضافة خدمة جديدة
                            </h1>
                            <p class="text-secondary-dark">إضافة خدمة جديدة إلى قائمة الخدمات</p>
                        </div>
                        <div>
                            <a href="{% url 'company:admin_services' %}" class="btn-secondary-dark">
                                <i class="fas fa-arrow-right"></i>العودة للخدمات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- رسائل النجاح والخطأ -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert-dark alert-{{ message.tags }}">
                            {% if message.tags == 'success' %}
                                <i class="fas fa-check-circle me-2"></i>
                            {% elif message.tags == 'warning' %}
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            {% elif message.tags == 'error' or message.tags == 'danger' %}
                                <i class="fas fa-times-circle me-2"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- نموذج إضافة الخدمة -->
                <form method="post" enctype="multipart/form-data" id="serviceForm">
                    {% csrf_token %}
                    
                    <!-- معلومات الخدمة الأساسية -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-info-circle me-2"></i>معلومات الخدمة الأساسية
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">اسم الخدمة *</label>
                                    <input type="text" class="form-control-dark" name="name" required placeholder="مثال: تركيب أنظمة المنازل الذكية">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">صورة الخدمة</label>
                                    <input type="file" class="form-control-dark" name="image" accept="image/*">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">وصف الخدمة *</label>
                                    <textarea class="form-control-dark" name="description" rows="4" required placeholder="اكتب وصفاً مفصلاً للخدمة"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الخدمة -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-dollar-sign me-2"></i>تفاصيل الخدمة
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">السعر</label>
                                    <input type="text" class="form-control-dark" name="price" placeholder="مثال: 500 دولار - 2000 دولار">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">مدة التنفيذ</label>
                                    <input type="text" class="form-control-dark" name="duration" placeholder="مثال: 1-3 أسابيع">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">الميزات والخصائص</label>
                                    <textarea class="form-control-dark" name="features" rows="4" placeholder="اكتب الميزات والخصائص المتضمنة في الخدمة (كل ميزة في سطر منفصل)"></textarea>
                                    <small class="text-muted-dark">اكتب كل ميزة في سطر منفصل</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات العرض -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-eye me-2"></i>إعدادات العرض
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="isActive" checked>
                                    <label class="form-check-label text-secondary-dark" for="isActive">
                                        خدمة نشطة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="isFeatured">
                                    <label class="form-check-label text-secondary-dark" for="isFeatured">
                                        خدمة مميزة
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">ترتيب العرض</label>
                                    <input type="number" class="form-control-dark" name="display_order" value="0" min="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="card-dark-pro">
                        <div class="d-flex gap-3 justify-content-center">
                            <button type="submit" class="btn-dark-pro">
                                <i class="fas fa-save"></i>حفظ الخدمة
                            </button>
                            <button type="reset" class="btn-outline-dark">
                                <i class="fas fa-undo"></i>إعادة تعيين
                            </button>
                            <a href="{% url 'company:admin_services' %}" class="btn-secondary-dark">
                                <i class="fas fa-times"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript خاص بإضافة الخدمة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من صحة النموذج
            const form = document.getElementById('serviceForm');
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.style.borderColor = 'var(--accent-red)';
                        isValid = false;
                    } else {
                        field.style.borderColor = 'var(--border-color)';
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    showNotification('يرجى ملء جميع الحقول المطلوبة', 'danger');
                    return;
                }
                
                // إظهار رسالة التحميل
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>
