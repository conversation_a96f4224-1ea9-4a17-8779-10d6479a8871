/* IQHome - تصميم إداري بسيط وأنيق */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #334155;
    --text-muted: #64748b;
}

* {
    box-sizing: border-box;
}

html {
    direction: rtl;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* شريط التنقل البسيط */
.navbar-simple {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-brand-simple {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

/* الشريط الجانبي البسيط */
.sidebar-simple {
    background: white;
    border-left: 1px solid var(--border-color);
    min-height: 100vh;
    padding: 2rem 0;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin: 0.5rem 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    margin: 0 1rem;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-menu i {
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 2rem;
    background-color: var(--light-color);
    min-height: 100vh;
}

/* البطاقات البسيطة */
.card-simple {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.card-header-simple {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

/* الأزرار البسيطة */
.btn-simple {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* الجداول البسيطة */
.table-simple {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-simple th {
    background-color: var(--light-color);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 1px solid var(--border-color);
}

.table-simple td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.table-simple tr:hover {
    background-color: #f1f5f9;
}

/* النماذج البسيطة */
.form-simple {
    background: white;
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* الإحصائيات البسيطة */
.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-muted);
    font-weight: 500;
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* الشارات البسيطة */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.badge-secondary {
    background-color: var(--secondary-color);
    color: white;
}

/* التنبيهات البسيطة */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.alert-danger {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-info {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar-simple {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar-simple.active {
        right: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .card-simple {
        padding: 1rem;
    }
    
    .table-simple {
        font-size: 0.875rem;
    }
    
    .table-simple th,
    .table-simple td {
        padding: 0.5rem;
    }
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
