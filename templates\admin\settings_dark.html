<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الموقع - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>{{ company_name }} - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}" class="active">
                            <i class="fas fa-cog"></i>
                            إعدادات الموقع
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-cog me-2"></i>إعدادات الموقع
                            </h1>
                            <p class="text-secondary-dark">تحكم في جميع إعدادات ومعلومات الموقع</p>
                        </div>
                        <div>
                            <button class="btn-outline-dark" onclick="previewChanges()">
                                <i class="fas fa-eye"></i>معاينة التغييرات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رسائل النجاح والخطأ -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert-dark alert-{{ message.tags }}">
                            {% if message.tags == 'success' %}
                                <i class="fas fa-check-circle me-2"></i>
                            {% elif message.tags == 'warning' %}
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            {% elif message.tags == 'error' or message.tags == 'danger' %}
                                <i class="fas fa-times-circle me-2"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- نموذج الإعدادات -->
                <form method="post" id="settingsForm">
                    {% csrf_token %}
                    
                    <!-- معلومات الشركة الأساسية -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-building me-2"></i>معلومات الشركة الأساسية
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">اسم الشركة (عربي) *</label>
                                    <input type="text" class="form-control-dark" name="company_name_ar" value="{{ settings.company_name_ar }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">اسم الشركة (إنجليزي) *</label>
                                    <input type="text" class="form-control-dark" name="company_name_en" value="{{ settings.company_name_en }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">شعار الشركة (عربي) *</label>
                                    <input type="text" class="form-control-dark" name="company_slogan_ar" value="{{ settings.company_slogan_ar }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">شعار الشركة (إنجليزي) *</label>
                                    <input type="text" class="form-control-dark" name="company_slogan_en" value="{{ settings.company_slogan_en }}" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التواصل -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-phone me-2"></i>معلومات التواصل
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">الهاتف الرئيسي *</label>
                                    <input type="tel" class="form-control-dark" name="phone_primary" value="{{ settings.phone_primary }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">الهاتف الثانوي</label>
                                    <input type="tel" class="form-control-dark" name="phone_secondary" value="{{ settings.phone_secondary }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">البريد الإلكتروني الرئيسي *</label>
                                    <input type="email" class="form-control-dark" name="email_primary" value="{{ settings.email_primary }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">بريد الدعم الفني</label>
                                    <input type="email" class="form-control-dark" name="email_support" value="{{ settings.email_support }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العنوان -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-map-marker-alt me-2"></i>معلومات العنوان
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">العنوان (عربي) *</label>
                                    <input type="text" class="form-control-dark" name="address_ar" value="{{ settings.address_ar }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">العنوان (إنجليزي) *</label>
                                    <input type="text" class="form-control-dark" name="address_en" value="{{ settings.address_en }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">تفاصيل العنوان (عربي)</label>
                                    <textarea class="form-control-dark" name="address_details_ar" rows="3">{{ settings.address_details_ar }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">تفاصيل العنوان (إنجليزي)</label>
                                    <textarea class="form-control-dark" name="address_details_en" rows="3">{{ settings.address_details_en }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">ساعات العمل (عربي)</label>
                                    <input type="text" class="form-control-dark" name="working_hours_ar" value="{{ settings.working_hours_ar }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">ساعات العمل (إنجليزي)</label>
                                    <input type="text" class="form-control-dark" name="working_hours_en" value="{{ settings.working_hours_en }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- وسائل التواصل الاجتماعي -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-share-alt me-2"></i>وسائل التواصل الاجتماعي
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">رابط فيسبوك</label>
                                    <input type="url" class="form-control-dark" name="facebook_url" value="{{ settings.facebook_url }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">رابط تويتر</label>
                                    <input type="url" class="form-control-dark" name="twitter_url" value="{{ settings.twitter_url }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">رابط إنستغرام</label>
                                    <input type="url" class="form-control-dark" name="instagram_url" value="{{ settings.instagram_url }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">رابط لينكد إن</label>
                                    <input type="url" class="form-control-dark" name="linkedin_url" value="{{ settings.linkedin_url }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">رقم واتساب</label>
                                    <input type="tel" class="form-control-dark" name="whatsapp_number" value="{{ settings.whatsapp_number }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الموقع -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-globe me-2"></i>إعدادات الموقع
                            </h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">عنوان الموقع (عربي)</label>
                                    <input type="text" class="form-control-dark" name="site_title_ar" value="{{ settings.site_title_ar }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">عنوان الموقع (إنجليزي)</label>
                                    <input type="text" class="form-control-dark" name="site_title_en" value="{{ settings.site_title_en }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">وصف الموقع (عربي)</label>
                                    <textarea class="form-control-dark" name="site_description_ar" rows="3">{{ settings.site_description_ar }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">وصف الموقع (إنجليزي)</label>
                                    <textarea class="form-control-dark" name="site_description_en" rows="3">{{ settings.site_description_en }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">من نحن (عربي)</label>
                                    <textarea class="form-control-dark" name="about_us_ar" rows="4">{{ settings.about_us_ar }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">من نحن (إنجليزي)</label>
                                    <textarea class="form-control-dark" name="about_us_en" rows="4">{{ settings.about_us_en }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الإحصائيات -->
                    <div class="card-dark-pro mb-4">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark">
                                <i class="fas fa-chart-bar me-2"></i>إعدادات الإحصائيات
                            </h3>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" name="show_stats" id="show_stats" {% if settings.show_stats %}checked{% endif %}>
                            <label class="form-check-label text-secondary-dark" for="show_stats">
                                عرض الإحصائيات في الموقع
                            </label>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">عدد المشاريع</label>
                                    <input type="number" class="form-control-dark" name="stats_projects" value="{{ settings.stats_projects }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">سنوات الخبرة</label>
                                    <input type="number" class="form-control-dark" name="stats_experience" value="{{ settings.stats_experience }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group-dark">
                                    <label class="form-label-dark">عدد العملاء</label>
                                    <input type="number" class="form-control-dark" name="stats_customers" value="{{ settings.stats_customers }}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="card-dark-pro">
                        <div class="d-flex gap-3 justify-content-center">
                            <button type="submit" class="btn-dark-pro">
                                <i class="fas fa-save"></i>حفظ الإعدادات
                            </button>
                            <button type="button" class="btn-outline-dark" onclick="resetForm()">
                                <i class="fas fa-undo"></i>إعادة تعيين
                            </button>
                            <a href="{% url 'company:admin_dashboard' %}" class="btn-secondary-dark">
                                <i class="fas fa-arrow-right"></i>العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript خاص بإعدادات الموقع -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('settingsForm');
            
            form.addEventListener('submit', function(e) {
                // التحقق من صحة البيانات
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.style.borderColor = 'var(--accent-red)';
                        isValid = false;
                    } else {
                        field.style.borderColor = 'var(--border-color)';
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    showNotification('يرجى ملء جميع الحقول المطلوبة', 'danger');
                    return;
                }
                
                // إظهار رسالة التحميل
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                submitBtn.disabled = true;
            });
        });
        
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الحقول؟')) {
                document.getElementById('settingsForm').reset();
                showNotification('تم إعادة تعيين النموذج', 'info');
            }
        }
        
        function previewChanges() {
            // فتح الموقع في نافذة جديدة لمعاينة التغييرات
            window.open('/', '_blank');
        }
    </script>
</body>
</html>
