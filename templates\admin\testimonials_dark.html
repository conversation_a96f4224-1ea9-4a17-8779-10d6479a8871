<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة آراء العملاء - {{ company_name }}</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>{{ company_name }} - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_portfolio' %}">
                            <i class="fas fa-images"></i>
                            معرض الأعمال
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_testimonials' %}" class="active">
                            <i class="fas fa-star"></i>
                            آراء العملاء
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_content' %}">
                            <i class="fas fa-file-alt"></i>
                            إدارة المحتوى
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}">
                            <i class="fas fa-cog"></i>
                            إعدادات الموقع
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-star me-2"></i>إدارة آراء العملاء
                            </h1>
                            <p class="text-secondary-dark">إدارة وتنظيم تقييمات وآراء العملاء</p>
                        </div>
                        <div>
                            <a href="{% url 'company:admin_testimonial_add' %}" class="btn-dark-pro">
                                <i class="fas fa-plus"></i>إضافة رأي جديد
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات آراء العملاء -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ total_testimonials }}</div>
                            <div class="stat-label-dark">إجمالي الآراء</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-comments"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ published_testimonials }}</div>
                            <div class="stat-label-dark">آراء منشورة</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ featured_testimonials }}</div>
                            <div class="stat-label-dark">آراء مميزة</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">
                                {% for rating, count in rating_stats.items %}
                                    {% if rating == "5_stars" %}{{ count }}{% endif %}
                                {% endfor %}
                            </div>
                            <div class="stat-label-dark">تقييم 5 نجوم</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-trophy"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات التقييمات -->
                <div class="row g-4 mb-4">
                    <div class="col-md-12">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-chart-bar me-2"></i>توزيع التقييمات
                                </h5>
                            </div>
                            <div class="row text-center">
                                {% for rating, count in rating_stats.items %}
                                <div class="col">
                                    <div class="stat-mini-dark">
                                        <div class="stat-number-mini">{{ count }}</div>
                                        <div class="stat-label-mini">
                                            {% if rating == "5_stars" %}5 نجوم{% endif %}
                                            {% if rating == "4_stars" %}4 نجوم{% endif %}
                                            {% if rating == "3_stars" %}3 نجوم{% endif %}
                                            {% if rating == "2_stars" %}2 نجوم{% endif %}
                                            {% if rating == "1_stars" %}1 نجم{% endif %}
                                        </div>
                                        <div class="text-warning">
                                            {% if rating == "5_stars" %}
                                                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                                            {% elif rating == "4_stars" %}
                                                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="far fa-star"></i>
                                            {% elif rating == "3_stars" %}
                                                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i>
                                            {% elif rating == "2_stars" %}
                                                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i>
                                            {% elif rating == "1_stars" %}
                                                <i class="fas fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة آراء العملاء -->
                <div class="card-dark-pro">
                    <div class="card-header-dark">
                        <h3 class="card-title-dark">
                            <i class="fas fa-list me-2"></i>قائمة آراء العملاء
                        </h3>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table-dark-pro">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>التقييم</th>
                                    <th>الرأي</th>
                                    <th>المشروع/الخدمة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for testimonial in testimonials %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if testimonial.client_image %}
                                                <img src="{{ testimonial.client_image.url }}" alt="{{ testimonial.client_name }}" 
                                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;" class="me-2">
                                            {% else %}
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <strong class="text-primary-dark">{{ testimonial.client_name }}</strong>
                                                {% if testimonial.client_position %}
                                                    <br><small class="text-muted-dark">{{ testimonial.client_position }}</small>
                                                {% endif %}
                                                {% if testimonial.client_company %}
                                                    <br><small class="text-muted-dark">{{ testimonial.client_company }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-warning">
                                            {% for i in testimonial.stars_range %}
                                                <i class="fas fa-star"></i>
                                            {% endfor %}
                                            {% for i in testimonial.empty_stars_range %}
                                                <i class="far fa-star"></i>
                                            {% endfor %}
                                        </div>
                                        <small class="text-muted-dark">{{ testimonial.rating }}/5</small>
                                    </td>
                                    <td>
                                        <div style="max-width: 300px;">
                                            <p class="text-secondary-dark mb-1">{{ testimonial.testimonial_ar|truncatewords:15 }}</p>
                                            {% if testimonial.is_featured %}
                                                <span class="badge-warning">
                                                    <i class="fas fa-star"></i>مميز
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if testimonial.portfolio %}
                                            <span class="badge-info">{{ testimonial.portfolio.title_ar|truncatewords:3 }}</span>
                                        {% elif testimonial.service %}
                                            <span class="badge-secondary">{{ testimonial.service.name }}</span>
                                        {% else %}
                                            <span class="text-muted-dark">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if testimonial.is_published %}
                                            <span class="badge-success">منشور</span>
                                        {% else %}
                                            <span class="badge-warning">مسودة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted-dark">{{ testimonial.created_at|date:"d/m/Y" }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'company:admin_testimonial_edit' testimonial.id %}" class="btn-outline-dark btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn-outline-danger btn-sm" onclick="deleteTestimonial('{{ testimonial.id }}')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-comments fa-2x text-muted-dark mb-2"></i>
                                        <p class="text-muted-dark">لا توجد آراء عملاء</p>
                                        <a href="/admin/company/testimonial/add/" class="btn-dark-pro">
                                            <i class="fas fa-plus"></i>إضافة أول رأي
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="row g-4 mt-4">
                    <div class="col-md-6">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-link me-2"></i>روابط سريعة
                                </h5>
                            </div>
                            <div class="d-grid gap-2">
                                <a href="/admin/company/testimonial/" class="btn-outline-dark">
                                    <i class="fas fa-cog"></i>إدارة الآراء (Django Admin)
                                </a>
                                <a href="{% url 'company:admin_portfolio' %}" class="btn-outline-dark">
                                    <i class="fas fa-images"></i>معرض الأعمال
                                </a>
                                <a href="{% url 'company:admin_services' %}" class="btn-outline-dark">
                                    <i class="fas fa-cogs"></i>إدارة الخدمات
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-info-circle me-2"></i>نصائح
                                </h5>
                            </div>
                            <ul class="list-unstyled mb-0">
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    اطلب من العملاء كتابة آرائهم بعد انتهاء المشروع
                                </li>
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    اربط كل رأي بالمشروع أو الخدمة المناسبة
                                </li>
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    حدد الآراء المميزة لعرضها في الصفحة الرئيسية
                                </li>
                                <li class="text-secondary-dark">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    أضف صور العملاء لزيادة المصداقية
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <script>
        function deleteTestimonial(testimonialId) {
            if (confirm('هل أنت متأكد من حذف رأي هذا العميل؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                // إنشاء نموذج مخفي للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/testimonials/delete/${testimonialId}/`;

                // إضافة CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        }

        // إضافة CSRF token للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = '{{ csrf_token }}';
                document.body.appendChild(csrfInput);
            }
        });
    </script>
</body>
</html>
