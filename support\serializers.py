from rest_framework import serializers
from .models import SupportTicket, TicketMessage
from devices.models import Device


class SupportTicketSerializer(serializers.ModelSerializer):
    """
    Serializer for Support Tickets
    """
    user_name = serializers.CharField(source='user.username', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    device_name = serializers.CharField(source='device_related.name', read_only=True)
    is_open = serializers.ReadOnlyField()
    
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'ticket_number', 'title', 'description', 'category',
            'priority', 'status', 'user', 'user_name', 'assigned_to',
            'assigned_to_name', 'device_related', 'device_name',
            'created_at', 'updated_at', 'resolved_at', 'closed_at',
            'attachments', 'is_open'
        ]
        read_only_fields = [
            'id', 'ticket_number', 'created_at', 'updated_at',
            'resolved_at', 'closed_at'
        ]


class SupportTicketCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating support tickets
    """
    
    class Meta:
        model = SupportTicket
        fields = [
            'title', 'description', 'category', 'priority',
            'device_related', 'attachments'
        ]
    
    def create(self, validated_data):
        # Set the user to the current user
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_device_related(self, value):
        """Ensure user can only reference their own devices"""
        if value:
            user = self.context['request'].user
            if not user.is_admin and value.user != user:
                raise serializers.ValidationError("You can only reference your own devices")
        return value


class SupportTicketUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating support tickets (admin only)
    """
    
    class Meta:
        model = SupportTicket
        fields = ['status', 'priority', 'assigned_to']


class TicketMessageSerializer(serializers.ModelSerializer):
    """
    Serializer for Ticket Messages
    """
    sender_name = serializers.CharField(source='sender.username', read_only=True)
    
    class Meta:
        model = TicketMessage
        fields = [
            'id', 'ticket', 'sender', 'sender_name', 'message',
            'is_internal', 'created_at', 'attachment'
        ]
        read_only_fields = ['id', 'created_at']


class TicketMessageCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating ticket messages
    """
    
    class Meta:
        model = TicketMessage
        fields = ['ticket', 'message', 'is_internal', 'attachment']
    
    def create(self, validated_data):
        # Set the sender to the current user
        validated_data['sender'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_ticket(self, value):
        """Ensure user can only add messages to accessible tickets"""
        user = self.context['request'].user
        if not user.is_admin and value.user != user:
            raise serializers.ValidationError("You can only add messages to your own tickets")
        return value
    
    def validate_is_internal(self, value):
        """Only admins can create internal messages"""
        user = self.context['request'].user
        if value and not user.is_admin:
            raise serializers.ValidationError("Only admins can create internal messages")
        return value


class SupportTicketDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for support tickets with messages
    """
    user_name = serializers.CharField(source='user.username', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    device_name = serializers.CharField(source='device_related.name', read_only=True)
    is_open = serializers.ReadOnlyField()
    messages = serializers.SerializerMethodField()
    
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'ticket_number', 'title', 'description', 'category',
            'priority', 'status', 'user', 'user_name', 'assigned_to',
            'assigned_to_name', 'device_related', 'device_name',
            'created_at', 'updated_at', 'resolved_at', 'closed_at',
            'attachments', 'is_open', 'messages'
        ]
    
    def get_messages(self, obj):
        """Get messages for this ticket"""
        user = self.context['request'].user
        messages = obj.messages.all()
        
        # Filter out internal messages for non-admin users
        if not user.is_admin:
            messages = messages.filter(is_internal=False)
        
        return TicketMessageSerializer(messages, many=True).data


class SupportStatsSerializer(serializers.Serializer):
    """
    Serializer for support statistics
    """
    total_tickets = serializers.IntegerField()
    open_tickets = serializers.IntegerField()
    resolved_tickets = serializers.IntegerField()
    closed_tickets = serializers.IntegerField()
    tickets_by_category = serializers.DictField()
    tickets_by_priority = serializers.DictField()
    recent_tickets = serializers.ListField()


class AdminTicketListSerializer(serializers.ModelSerializer):
    """
    Admin serializer for listing tickets with additional info
    """
    user_name = serializers.CharField(source='user.username', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.username', read_only=True)
    device_name = serializers.CharField(source='device_related.name', read_only=True)
    message_count = serializers.SerializerMethodField()
    
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'ticket_number', 'title', 'category', 'priority',
            'status', 'user', 'user_name', 'user_email', 'assigned_to',
            'assigned_to_name', 'device_name', 'created_at',
            'updated_at', 'message_count'
        ]
    
    def get_message_count(self, obj):
        """Get the number of messages in this ticket"""
        return obj.messages.count()
