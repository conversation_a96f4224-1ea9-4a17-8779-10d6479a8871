from rest_framework import serializers
from .models import Device, DeviceD<PERSON>, ControlCommand


class DeviceSerializer(serializers.ModelSerializer):
    """
    Serializer for Device model
    """
    user_name = serializers.CharField(source='user.username', read_only=True)
    is_online = serializers.ReadOnlyField()
    
    class Meta:
        model = Device
        fields = [
            'id', 'name', 'device_type', 'mac_address', 'ip_address',
            'status', 'is_active', 'user', 'user_name', 'location',
            'model_number', 'firmware_version', 'manufacturer',
            'created_at', 'updated_at', 'last_seen', 'is_online'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_seen']
    
    def validate_mac_address(self, value):
        """Validate MAC address format"""
        import re
        mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
        if not re.match(mac_pattern, value):
            raise serializers.ValidationError("Invalid MAC address format")
        return value


class DeviceCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new devices
    """
    
    class Meta:
        model = Device
        fields = [
            'name', 'device_type', 'mac_address', 'ip_address',
            'location', 'model_number', 'firmware_version', 'manufacturer'
        ]
    
    def create(self, validated_data):
        # Set the user to the current user
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class DeviceUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating device information
    """
    
    class Meta:
        model = Device
        fields = [
            'name', 'location', 'ip_address', 'status', 'is_active',
            'model_number', 'firmware_version', 'manufacturer'
        ]


class DeviceDataSerializer(serializers.ModelSerializer):
    """
    Serializer for Device Data
    """
    device_name = serializers.CharField(source='device.name', read_only=True)
    
    class Meta:
        model = DeviceData
        fields = [
            'id', 'device', 'device_name', 'timestamp', 'key', 'value', 'unit'
        ]
        read_only_fields = ['id', 'timestamp']


class DeviceDataCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating device data points
    """
    
    class Meta:
        model = DeviceData
        fields = ['device', 'key', 'value', 'unit']
    
    def validate_device(self, value):
        """Ensure user can only add data to their own devices"""
        user = self.context['request'].user
        if not user.is_admin and value.user != user:
            raise serializers.ValidationError("You can only add data to your own devices")
        return value


class ControlCommandSerializer(serializers.ModelSerializer):
    """
    Serializer for Control Commands
    """
    device_name = serializers.CharField(source='device.name', read_only=True)
    issued_by_name = serializers.CharField(source='issued_by.username', read_only=True)
    
    class Meta:
        model = ControlCommand
        fields = [
            'id', 'device', 'device_name', 'command_type', 'command_data',
            'status', 'issued_by', 'issued_by_name', 'timestamp',
            'sent_at', 'completed_at', 'response_data', 'error_message'
        ]
        read_only_fields = [
            'id', 'timestamp', 'sent_at', 'completed_at', 'response_data', 'error_message'
        ]


class ControlCommandCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating control commands
    """
    
    class Meta:
        model = ControlCommand
        fields = ['device', 'command_type', 'command_data']
    
    def create(self, validated_data):
        # Set the user who issued the command
        validated_data['issued_by'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_device(self, value):
        """Ensure user can only control their own devices"""
        user = self.context['request'].user
        if not user.is_admin and value.user != user:
            raise serializers.ValidationError("You can only control your own devices")
        return value


class DeviceStatsSerializer(serializers.Serializer):
    """
    Serializer for device statistics
    """
    total_devices = serializers.IntegerField()
    online_devices = serializers.IntegerField()
    offline_devices = serializers.IntegerField()
    device_types = serializers.DictField()
    recent_activity = serializers.ListField()


class DeviceDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for device with related data
    """
    user_name = serializers.CharField(source='user.username', read_only=True)
    is_online = serializers.ReadOnlyField()
    recent_data = serializers.SerializerMethodField()
    recent_commands = serializers.SerializerMethodField()
    
    class Meta:
        model = Device
        fields = [
            'id', 'name', 'device_type', 'mac_address', 'ip_address',
            'status', 'is_active', 'user', 'user_name', 'location',
            'model_number', 'firmware_version', 'manufacturer',
            'created_at', 'updated_at', 'last_seen', 'is_online',
            'recent_data', 'recent_commands'
        ]
    
    def get_recent_data(self, obj):
        """Get recent data points for this device"""
        recent_data = obj.data_points.order_by('-timestamp')[:10]
        return DeviceDataSerializer(recent_data, many=True).data
    
    def get_recent_commands(self, obj):
        """Get recent commands for this device"""
        recent_commands = obj.commands.order_by('-timestamp')[:10]
        return ControlCommandSerializer(recent_commands, many=True).data
