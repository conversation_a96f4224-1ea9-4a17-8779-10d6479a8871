# 🔧 دليل حل المشاكل - IQHome Smart Home System

## 🚨 المشاكل الشائعة وحلولها

---

## 1. **مشكلة الصور المفقودة في معرض الأعمال**

### 🔍 **الخطأ**:
```
ValueError: The 'featured_image' attribute has no file associated with it.
```

### ✅ **الحل**:
تم حل هذه المشكلة بإضافة فحص للصور في القوالب:

```html
{% if project.featured_image %}
    <img src="{{ project.featured_image.url }}" alt="{{ project.title_ar }}">
{% else %}
    <!-- صورة افتراضية -->
    <div class="placeholder-image">
        <i class="fas fa-image"></i>
        <p>لا توجد صورة</p>
    </div>
{% endif %}
```

### 🛠️ **إضافة صور للمشاريع**:
1. **من Django Admin**: `/admin/company/portfolio/`
2. **تحديد المشروع** والنقر على "تحرير"
3. **رفع صورة** في حقل "الصورة الرئيسية"
4. **حفظ التغييرات**

---

## 2. **مشكلة عدم ظهور التغييرات**

### 🔍 **الأعراض**:
- التغييرات لا تظهر في الموقع
- المحتوى القديم ما زال يظهر

### ✅ **الحلول**:

#### أ) **مسح الـ Cache**:
```python
# في Django Shell
from django.core.cache import cache
cache.clear()
```

#### ب) **إعادة تشغيل الخادم**:
```bash
# إيقاف الخادم (Ctrl+C)
# ثم إعادة التشغيل
python manage.py runserver 8001
```

#### ج) **تحديث المتصفح**:
- **Ctrl + F5** (Windows)
- **Cmd + Shift + R** (Mac)

---

## 3. **مشكلة الصلاحيات**

### 🔍 **الخطأ**:
```
Permission denied / Access forbidden
```

### ✅ **الحل**:
```python
# إنشاء مستخدم مدير جديد
python manage.py createsuperuser

# أو تحديث صلاحيات مستخدم موجود
python manage.py shell
```

```python
from accounts.models import User
user = User.objects.get(email='<EMAIL>')
user.is_admin = True
user.is_staff = True
user.is_superuser = True
user.save()
```

---

## 4. **مشكلة قاعدة البيانات**

### 🔍 **الأخطاء الشائعة**:
- `no such table`
- `column doesn't exist`
- `migration errors`

### ✅ **الحلول**:

#### أ) **تطبيق Migrations**:
```bash
python manage.py makemigrations
python manage.py migrate
```

#### ب) **إعادة إنشاء قاعدة البيانات** (احذر: يحذف البيانات):
```bash
# حذف ملف قاعدة البيانات
del db.sqlite3

# إعادة إنشاء قاعدة البيانات
python manage.py migrate
python manage.py createsuperuser
```

---

## 5. **مشكلة الملفات الثابتة (Static Files)**

### 🔍 **الأعراض**:
- CSS/JS لا يعمل
- الصور لا تظهر
- التصميم مكسور

### ✅ **الحل**:
```bash
# جمع الملفات الثابتة
python manage.py collectstatic

# التأكد من إعدادات STATIC في settings.py
```

---

## 6. **مشكلة رفع الملفات**

### 🔍 **الخطأ**:
```
SuspiciousOperation: The joined path is located outside of the base path component
```

### ✅ **الحل**:
```python
# في settings.py
import os

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# في urls.py الرئيسي
from django.conf import settings
from django.conf.urls.static import static

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

---

## 7. **مشكلة الترميز (Encoding)**

### 🔍 **الأعراض**:
- النصوص العربية تظهر كرموز غريبة
- أخطاء في عرض المحتوى

### ✅ **الحل**:
```python
# في settings.py
DEFAULT_CHARSET = 'utf-8'
FILE_CHARSET = 'utf-8'

# في قوالب HTML
<meta charset="UTF-8">
```

---

## 8. **مشكلة الأداء البطيء**

### 🔍 **الأعراض**:
- الموقع بطيء في التحميل
- استعلامات قاعدة البيانات كثيرة

### ✅ **الحلول**:

#### أ) **تفعيل التخزين المؤقت**:
```python
# في views.py
from django.core.cache import cache

def get_cached_data(key, queryset, timeout=3600):
    data = cache.get(key)
    if not data:
        data = list(queryset)
        cache.set(key, data, timeout)
    return data
```

#### ب) **تحسين الاستعلامات**:
```python
# استخدام select_related و prefetch_related
Portfolio.objects.select_related('service').prefetch_related('images')
```

---

## 9. **مشكلة النسخ الاحتياطي**

### 🔍 **الحاجة**:
- حفظ نسخة احتياطية من البيانات
- استعادة البيانات المحذوفة

### ✅ **الحل**:

#### أ) **إنشاء نسخة احتياطية**:
```bash
# تصدير البيانات
python manage.py dumpdata > backup.json

# تصدير بيانات تطبيق معين
python manage.py dumpdata company > company_backup.json
```

#### ب) **استعادة النسخة الاحتياطية**:
```bash
# استعادة البيانات
python manage.py loaddata backup.json
```

---

## 10. **مشكلة البريد الإلكتروني**

### 🔍 **الخطأ**:
```
SMTPException / Email not sending
```

### ✅ **الحل**:
```python
# في settings.py
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

---

## 🚀 **نصائح للصيانة**

### ✅ **صيانة دورية**:
1. **نسخ احتياطية أسبوعية** من قاعدة البيانات
2. **تحديث Django** والمكتبات بانتظام
3. **مراقبة مساحة التخزين** للملفات المرفوعة
4. **فحص الأمان** والثغرات

### ✅ **مراقبة الأداء**:
1. **استخدام Django Debug Toolbar** في التطوير
2. **مراقبة استعلامات قاعدة البيانات**
3. **تحسين الصور** المرفوعة
4. **استخدام CDN** للملفات الثابتة

---

## 📞 **الحصول على المساعدة**

### 🔍 **عند مواجهة مشكلة جديدة**:
1. **تحقق من سجلات الأخطاء** في Terminal
2. **ابحث عن الخطأ** في Google
3. **راجع التوثيق** الرسمي لـ Django
4. **اسأل في المجتمعات** مثل Stack Overflow

### 📚 **مصادر مفيدة**:
- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Bootstrap Documentation](https://getbootstrap.com/docs/)

---

## ✅ **خلاصة**

معظم المشاكل الشائعة لها حلول بسيطة:
- **إعادة تشغيل الخادم** يحل 50% من المشاكل
- **مسح الـ Cache** يحل 30% من المشاكل
- **تطبيق Migrations** يحل 15% من المشاكل
- **باقي المشاكل** تحتاج تدخل تقني

**الموقع مصمم ليكون مستقر وموثوق!** 🎯✨
