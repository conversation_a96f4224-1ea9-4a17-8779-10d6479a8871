# Generated by Django 5.2.4 on 2025-07-29 04:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name_ar', models.CharField(default='IQHome', max_length=100, verbose_name='اسم الشركة (عربي)')),
                ('company_name_en', models.CharField(default='IQHome', max_length=100, verbose_name='اسم الشركة (إنجليزي)')),
                ('company_slogan_ar', models.CharField(default='الشركة الرائدة في تقنيات المنازل الذكية في العراق', max_length=200, verbose_name='شعار الشركة (عربي)')),
                ('company_slogan_en', models.CharField(default='Leading Smart Home Technology Company in Iraq', max_length=200, verbose_name='شعار الشركة (إنجليزي)')),
                ('phone_primary', models.CharField(default='+964 ************', max_length=20, verbose_name='الهاتف الرئيسي')),
                ('phone_secondary', models.CharField(blank=True, max_length=20, verbose_name='الهاتف الثانوي')),
                ('email_primary', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='البريد الإلكتروني الرئيسي')),
                ('email_support', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='بريد الدعم الفني')),
                ('address_ar', models.CharField(default='بغداد - الكرادة الداخل', max_length=300, verbose_name='العنوان (عربي)')),
                ('address_en', models.CharField(default='Baghdad - Karrada', max_length=300, verbose_name='العنوان (إنجليزي)')),
                ('address_details_ar', models.TextField(default='شارع الكرادة الداخل - مقابل مجمع بابل التجاري', verbose_name='تفاصيل العنوان (عربي)')),
                ('address_details_en', models.TextField(default='Karrada Street - Opposite Babel Commercial Complex', verbose_name='تفاصيل العنوان (إنجليزي)')),
                ('working_hours_ar', models.CharField(default='السبت - الخميس: 9:00 ص - 6:00 م', max_length=100, verbose_name='ساعات العمل (عربي)')),
                ('working_hours_en', models.CharField(default='Saturday - Thursday: 9:00 AM - 6:00 PM', max_length=100, verbose_name='ساعات العمل (إنجليزي)')),
                ('facebook_url', models.URLField(blank=True, verbose_name='رابط فيسبوك')),
                ('twitter_url', models.URLField(blank=True, verbose_name='رابط تويتر')),
                ('instagram_url', models.URLField(blank=True, verbose_name='رابط إنستغرام')),
                ('linkedin_url', models.URLField(blank=True, verbose_name='رابط لينكد إن')),
                ('whatsapp_number', models.CharField(blank=True, max_length=20, verbose_name='رقم واتساب')),
                ('site_title_ar', models.CharField(default='IQHome - المنازل الذكية', max_length=100, verbose_name='عنوان الموقع (عربي)')),
                ('site_title_en', models.CharField(default='IQHome - Smart Homes', max_length=100, verbose_name='عنوان الموقع (إنجليزي)')),
                ('site_description_ar', models.TextField(default='نحول منزلك إلى منزل ذكي عصري وآمن بأحدث التقنيات العالمية', verbose_name='وصف الموقع (عربي)')),
                ('site_description_en', models.TextField(default='Transform your home into a modern and secure smart home with the latest global technologies', verbose_name='وصف الموقع (إنجليزي)')),
                ('about_us_ar', models.TextField(default='IQHome هي الشركة الرائدة في مجال تقنيات المنازل الذكية في العراق، نتميز بخبرة واسعة وفريق متخصص في أحدث التقنيات العالمية.', verbose_name='من نحن (عربي)')),
                ('about_us_en', models.TextField(default='IQHome is the leading company in smart home technologies in Iraq, distinguished by extensive experience and a specialized team in the latest global technologies.', verbose_name='من نحن (إنجليزي)')),
                ('show_stats', models.BooleanField(default=True, verbose_name='عرض الإحصائيات')),
                ('stats_projects', models.IntegerField(default=500, verbose_name='عدد المشاريع')),
                ('stats_experience', models.IntegerField(default=5, verbose_name='سنوات الخبرة')),
                ('stats_customers', models.IntegerField(default=1000, verbose_name='عدد العملاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات الموقع',
                'verbose_name_plural': 'إعدادات الموقع',
                'db_table': 'site_settings',
            },
        ),
    ]
