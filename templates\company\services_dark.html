<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خدماتنا - IQHome</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container">
            <a class="navbar-brand-dark" href="{% url 'company:home' %}">
                <i class="fas fa-home me-2"></i>IQHome
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link-dark" href="{% url 'company:home' %}">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link-dark active" href="{% url 'company:services' %}">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link-dark" href="{% url 'company:contact' %}">تواصل معنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="{% url 'company:request_service' %}" class="btn-dark-pro">
                        <i class="fas fa-phone"></i>اطلب خدمة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- رأس الصفحة -->
    <section class="hero-dark-pro section-with-navbar">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto hero-content-dark">
                    <h1 class="hero-title-dark">خدماتنا المتميزة</h1>
                    <p class="hero-subtitle-dark">
                        نقدم مجموعة شاملة من الخدمات المتخصصة في تقنيات المنازل الذكية
                        <br>
                        بأعلى معايير الجودة والاحترافية
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الخدمات -->
    <section class="py-5">
        <div class="container">
            <!-- فلترة الخدمات -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card-dark-pro">
                        <div class="d-flex flex-wrap gap-2 justify-content-center">
                            <button class="btn-dark-pro filter-btn active" data-filter="all">
                                <i class="fas fa-th"></i>جميع الخدمات
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="installation">
                                <i class="fas fa-home"></i>التركيب
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="maintenance">
                                <i class="fas fa-tools"></i>الصيانة
                            </button>
                            <button class="btn-outline-dark filter-btn" data-filter="consultation">
                                <i class="fas fa-user-tie"></i>الاستشارات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شبكة الخدمات -->
            <div class="row g-4" id="servicesGrid">
                {% for service in services %}
                <div class="col-lg-4 col-md-6 service-item" data-category="{{ service.category }}">
                    <div class="service-card-dark h-100">
                        <div class="service-icon-dark">
                            {% if service.category == 'installation' %}
                                <i class="fas fa-home"></i>
                            {% elif service.category == 'maintenance' %}
                                <i class="fas fa-tools"></i>
                            {% elif service.category == 'consultation' %}
                                <i class="fas fa-user-tie"></i>
                            {% else %}
                                <i class="fas fa-cogs"></i>
                            {% endif %}
                        </div>
                        
                        <h4 class="text-primary-dark mb-3">{{ service.name_ar }}</h4>
                        <p class="text-secondary-dark mb-3">{{ service.description_ar|truncatewords:20 }}</p>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted-dark">السعر:</span>
                                <strong class="text-accent">{{ service.price_range_display }}</strong>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted-dark">المدة:</span>
                                <span class="text-secondary-dark">{{ service.duration_display }}</span>
                            </div>
                            {% if service.is_featured %}
                                <div class="text-center">
                                    <span class="badge-primary">خدمة مميزة</span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mt-auto">
                            <div class="d-flex gap-2">
                                <a href="{% url 'company:service_detail' service.id %}" class="btn-outline-dark flex-fill">
                                    <i class="fas fa-info-circle"></i>التفاصيل
                                </a>
                                <a href="{% url 'company:request_service' %}?service={{ service.id }}" class="btn-dark-pro flex-fill">
                                    <i class="fas fa-phone"></i>اطلب الآن
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="card-dark-pro text-center">
                        <div class="py-5">
                            <i class="fas fa-exclamation-circle fa-3x text-muted-dark mb-3"></i>
                            <h4 class="text-secondary-dark">لا توجد خدمات متاحة حالياً</h4>
                            <p class="text-muted-dark">يرجى المحاولة مرة أخرى لاحقاً</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- إحصائيات الخدمات -->
            {% if services %}
            <div class="row g-4 mt-5">
                <div class="col-12">
                    <div class="card-dark-pro">
                        <div class="card-header-dark">
                            <h3 class="card-title-dark text-center">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات خدماتنا
                            </h3>
                        </div>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ services|length }}</div>
                                    <div class="stat-label-dark">إجمالي الخدمات</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">{{ featured_count }}</div>
                                    <div class="stat-label-dark">خدمات مميزة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">24/7</div>
                                    <div class="stat-label-dark">دعم فني</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card-dark">
                                    <div class="stat-number-dark">100%</div>
                                    <div class="stat-label-dark">ضمان الجودة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- دعوة للعمل -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card-dark-pro text-center">
                        <div class="py-4">
                            <h3 class="text-primary-dark mb-3">هل تحتاج لاستشارة مجانية؟</h3>
                            <p class="text-secondary-dark mb-4">
                                فريقنا المتخصص جاهز لمساعدتك في اختيار الخدمة المناسبة لاحتياجاتك
                            </p>
                            <div class="d-flex gap-3 justify-content-center flex-wrap">
                                <a href="{% url 'company:request_service' %}" class="btn-dark-pro">
                                    <i class="fas fa-phone"></i>اطلب استشارة مجانية
                                </a>
                                <a href="{% url 'company:contact' %}" class="btn-outline-dark">
                                    <i class="fas fa-envelope"></i>تواصل معنا
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل الاحترافي -->
    <footer class="footer-dark-pro">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-primary-dark">IQHome</h4>
                    <p class="text-secondary-dark">الشركة الرائدة في تقنيات المنازل الذكية في العراق</p>
                </div>
                <div class="col-md-6">
                    <h5 class="text-primary-dark">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'company:home' %}" class="text-secondary-dark text-decoration-none">الرئيسية</a></li>
                        <li><a href="{% url 'company:contact' %}" class="text-secondary-dark text-decoration-none">تواصل معنا</a></li>
                        <li><a href="{% url 'company:admin_login' %}" class="text-secondary-dark text-decoration-none">دخول الإدارة</a></li>
                    </ul>
                </div>
            </div>
            <hr style="border-color: var(--border-color);" class="my-4">
            <p class="text-center text-muted-dark">&copy; 2025 IQHome. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <!-- JavaScript للفلترة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const serviceItems = document.querySelectorAll('.service-item');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;
                    
                    // تحديث الأزرار النشطة
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.remove('btn-dark-pro');
                        btn.classList.add('btn-outline-dark');
                    });
                    
                    this.classList.add('active');
                    this.classList.remove('btn-outline-dark');
                    this.classList.add('btn-dark-pro');
                    
                    // فلترة الخدمات
                    serviceItems.forEach(item => {
                        if (filter === 'all' || item.dataset.category === filter) {
                            item.style.display = 'block';
                            item.style.opacity = '0';
                            setTimeout(() => {
                                item.style.opacity = '1';
                            }, 100);
                        } else {
                            item.style.opacity = '0';
                            setTimeout(() => {
                                item.style.display = 'none';
                            }, 300);
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
