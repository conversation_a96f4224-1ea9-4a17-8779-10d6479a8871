from django.contrib import admin
from .models import (
    Service, ServiceRequest, ContactMessage, SiteSettings,
    Portfolio, PortfolioImage, Testimonial, FAQ, BlogPost, Team, Partner
)


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """
    إدارة الخدمات
    """
    list_display = ('name_ar', 'category', 'price_range_display', 'is_active', 'is_featured', 'created_at')
    list_filter = ('category', 'is_active', 'is_featured', 'created_at')
    search_fields = ('name_ar', 'name_en', 'description_ar')
    ordering = ('-created_at',)

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name_ar', 'name_en', 'category')
        }),
        ('الوصف', {
            'fields': ('description_ar', 'description_en')
        }),
        ('التسعير والمدة', {
            'fields': ('price_range_min', 'price_range_max', 'duration_hours')
        }),
        ('الصور', {
            'fields': ('main_image', 'gallery_images')
        }),
        ('التفاصيل', {
            'fields': ('features', 'requirements'),
            'classes': ('collapse',)
        }),
        ('الحالة', {
            'fields': ('is_active', 'is_featured')
        }),
    )


@admin.register(ServiceRequest)
class ServiceRequestAdmin(admin.ModelAdmin):
    """
    إدارة طلبات الخدمات
    """
    list_display = ('request_number', 'customer_name', 'service', 'status', 'priority', 'created_at')
    list_filter = ('status', 'priority', 'service__category', 'customer_city', 'created_at')
    search_fields = ('request_number', 'customer_name', 'customer_phone', 'customer_email')
    ordering = ('-created_at',)
    readonly_fields = ('request_number', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('request_number', 'service', 'status', 'priority', 'assigned_to')
        }),
        ('بيانات العميل', {
            'fields': ('customer_name', 'customer_phone', 'customer_email', 'customer_city', 'customer_address')
        }),
        ('تفاصيل الطلب', {
            'fields': ('description', 'preferred_date', 'preferred_time', 'customer_notes')
        }),
        ('التكلفة', {
            'fields': ('estimated_cost', 'final_cost'),
            'classes': ('collapse',)
        }),
        ('ملاحظات الإدارة', {
            'fields': ('admin_notes',),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('service', 'assigned_to')


@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    """
    إدارة رسائل التواصل
    """
    list_display = ('name', 'subject', 'message_type', 'is_read', 'is_replied', 'created_at')
    list_filter = ('message_type', 'is_read', 'is_replied', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)

    fieldsets = (
        ('معلومات المرسل', {
            'fields': ('name', 'email', 'phone')
        }),
        ('الرسالة', {
            'fields': ('subject', 'message', 'message_type')
        }),
        ('حالة الرسالة', {
            'fields': ('is_read', 'is_replied')
        }),
        ('الرد', {
            'fields': ('reply_message', 'replied_by', 'replied_at'),
            'classes': ('collapse',)
        }),
        ('التاريخ', {
            'fields': ('created_at',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('replied_by')


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    """
    إدارة إعدادات الموقع
    """
    list_display = ['company_name_ar', 'phone_primary', 'email_primary', 'updated_at']

    fieldsets = (
        ('معلومات الشركة', {
            'fields': ('company_name_ar', 'company_name_en', 'company_slogan_ar', 'company_slogan_en')
        }),
        ('معلومات التواصل', {
            'fields': ('phone_primary', 'phone_secondary', 'email_primary', 'email_support')
        }),
        ('العنوان', {
            'fields': ('address_ar', 'address_en', 'address_details_ar', 'address_details_en', 'working_hours_ar', 'working_hours_en')
        }),
        ('وسائل التواصل الاجتماعي', {
            'fields': ('facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url', 'whatsapp_number')
        }),
        ('إعدادات الموقع', {
            'fields': ('site_title_ar', 'site_title_en', 'site_description_ar', 'site_description_en', 'about_us_ar', 'about_us_en')
        }),
        ('الإحصائيات', {
            'fields': ('show_stats', 'stats_projects', 'stats_experience', 'stats_customers')
        }),
    )

    def has_add_permission(self, request):
        # السماح بإضافة إعدادات واحدة فقط
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # منع حذف الإعدادات
        return False


class PortfolioImageInline(admin.TabularInline):
    """
    صور إضافية للمشروع
    """
    model = PortfolioImage
    extra = 3
    fields = ['image', 'caption_ar', 'display_order']


@admin.register(Portfolio)
class PortfolioAdmin(admin.ModelAdmin):
    """
    إدارة معرض الأعمال
    """
    list_display = ['title_ar', 'category', 'media_type', 'is_featured', 'is_published', 'show_on_homepage', 'display_order', 'created_at']
    list_filter = ['category', 'media_type', 'is_featured', 'is_published', 'show_on_homepage', 'created_at']
    search_fields = ['title_ar', 'title_en', 'description_ar', 'client_name', 'location']
    list_editable = ['is_featured', 'is_published', 'show_on_homepage', 'display_order']

    fieldsets = (
        ('معلومات المشروع الأساسية', {
            'fields': ('title_ar', 'title_en', 'description_ar', 'description_en', 'category', 'service')
        }),
        ('معلومات العميل والمشروع', {
            'fields': ('client_name', 'location', 'project_date', 'duration', 'project_cost')
        }),
        ('الوسائط', {
            'fields': ('media_type', 'featured_image', 'video_url', 'video_file')
        }),
        ('إعدادات العرض', {
            'fields': ('is_featured', 'is_published', 'show_on_homepage', 'display_order')
        }),
        ('معلومات إضافية', {
            'fields': ('technologies_used', 'client_feedback'),
            'classes': ('collapse',)
        }),
    )

    inlines = [PortfolioImageInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('service')


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    """
    إدارة آراء العملاء
    """
    list_display = ['client_name', 'rating', 'is_featured', 'is_published', 'show_on_homepage', 'created_at']
    list_filter = ['rating', 'is_featured', 'is_published', 'show_on_homepage', 'created_at']
    search_fields = ['client_name', 'client_company', 'testimonial_ar']
    list_editable = ['is_featured', 'is_published', 'show_on_homepage']

    fieldsets = (
        ('معلومات العميل', {
            'fields': ('client_name', 'client_position', 'client_company', 'client_image')
        }),
        ('الرأي والتقييم', {
            'fields': ('testimonial_ar', 'testimonial_en', 'rating')
        }),
        ('الربط', {
            'fields': ('portfolio', 'service')
        }),
        ('إعدادات العرض', {
            'fields': ('is_featured', 'is_published', 'show_on_homepage')
        }),
    )


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    """
    إدارة الأسئلة الشائعة
    """
    list_display = ['question_ar', 'category', 'display_order', 'is_published', 'created_at']
    list_filter = ['category', 'is_published', 'created_at']
    search_fields = ['question_ar', 'answer_ar']
    list_editable = ['display_order', 'is_published']

    fieldsets = (
        ('السؤال والإجابة', {
            'fields': ('question_ar', 'question_en', 'answer_ar', 'answer_en')
        }),
        ('التصنيف والعرض', {
            'fields': ('category', 'display_order', 'is_published')
        }),
    )


@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    """
    إدارة المقالات والأخبار
    """
    list_display = ['title_ar', 'author', 'category', 'is_featured', 'is_published', 'created_at']
    list_filter = ['category', 'is_featured', 'is_published', 'author', 'created_at']
    search_fields = ['title_ar', 'title_en', 'content_ar', 'tags']
    list_editable = ['is_featured', 'is_published']
    prepopulated_fields = {'slug': ('title_ar',)}

    fieldsets = (
        ('المحتوى الأساسي', {
            'fields': ('title_ar', 'title_en', 'slug', 'excerpt_ar', 'excerpt_en')
        }),
        ('المحتوى الكامل', {
            'fields': ('content_ar', 'content_en', 'featured_image')
        }),
        ('التصنيف والكلمات المفتاحية', {
            'fields': ('category', 'tags', 'author')
        }),
        ('إعدادات النشر', {
            'fields': ('is_featured', 'is_published')
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان مقال جديد
            obj.author = request.user
        super().save_model(request, obj, form, change)


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    """
    إدارة فريق العمل
    """
    list_display = ['name_ar', 'position_ar', 'display_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name_ar', 'name_en', 'position_ar', 'bio_ar']
    list_editable = ['display_order', 'is_active']

    fieldsets = (
        ('المعلومات الشخصية', {
            'fields': ('name_ar', 'name_en', 'position_ar', 'position_en', 'photo')
        }),
        ('النبذة الشخصية', {
            'fields': ('bio_ar', 'bio_en')
        }),
        ('معلومات التواصل', {
            'fields': ('email', 'phone', 'linkedin_url', 'twitter_url')
        }),
        ('إعدادات العرض', {
            'fields': ('display_order', 'is_active')
        }),
    )


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    """
    إدارة الشركاء والعملاء
    """
    list_display = ['name_ar', 'partner_type', 'display_order', 'is_active', 'show_on_homepage', 'created_at']
    list_filter = ['partner_type', 'is_active', 'show_on_homepage', 'created_at']
    search_fields = ['name_ar', 'name_en', 'description_ar']
    list_editable = ['display_order', 'is_active', 'show_on_homepage']

    fieldsets = (
        ('معلومات الشريك', {
            'fields': ('name_ar', 'name_en', 'logo', 'website_url', 'partner_type')
        }),
        ('الوصف', {
            'fields': ('description_ar', 'description_en')
        }),
        ('إعدادات العرض', {
            'fields': ('display_order', 'is_active', 'show_on_homepage')
        }),
    )
