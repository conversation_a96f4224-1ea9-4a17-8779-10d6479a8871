<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الخدمات - IQHome Control</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Admin Futuristic CSS -->
    <link href="/static/css/admin-futuristic.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-cyber fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand navbar-brand-cyber" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>IQHome Control
            </a>
            
            <div class="d-flex align-items-center">
                <a href="{% url 'company:admin_dashboard' %}" class="btn btn-neon me-3">
                    <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                </a>
                
                <div class="dropdown">
                    <button class="btn btn-cyber dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-shield me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item" href="{% url 'company:admin_logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar-cyber">
                <ul class="sidebar-menu">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}" class="active">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_analytics' %}">
                            <i class="fas fa-chart-line"></i>
                            التحليلات
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="dashboard-header fade-in-cyber">
                    <h1 class="dashboard-title">
                        <i class="fas fa-cogs me-3"></i>إدارة الخدمات
                    </h1>
                    <p class="dashboard-subtitle">إدارة وتحرير جميع خدمات الشركة</p>
                </div>

                <!-- أدوات التحكم والفلترة -->
                <div class="card-cyber mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <form method="get" class="d-flex gap-3 align-items-end">
                                    <div class="flex-fill">
                                        <label class="form-label-cyber">فئة الخدمة</label>
                                        <select name="category" class="form-control-cyber">
                                            <option value="">جميع الفئات</option>
                                            {% for value, label in categories %}
                                                <option value="{{ value }}" {% if selected_category == value %}selected{% endif %}>
                                                    {{ label }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div class="flex-fill">
                                        <label class="form-label-cyber">حالة الخدمة</label>
                                        <select name="status" class="form-control-cyber">
                                            <option value="">جميع الحالات</option>
                                            <option value="active" {% if selected_status == 'active' %}selected{% endif %}>نشطة</option>
                                            <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>غير نشطة</option>
                                            <option value="featured" {% if selected_status == 'featured' %}selected{% endif %}>مميزة</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-cyber">
                                        <i class="fas fa-filter me-2"></i>فلترة
                                    </button>
                                </form>
                            </div>
                            
                            <div class="col-md-4 text-end">
                                <button class="btn btn-neon" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                                    <i class="fas fa-plus me-2"></i>إضافة خدمة جديدة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الخدمات -->
                <div class="table-cyber scroll-reveal-cyber">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الخدمة</th>
                                <th>الفئة</th>
                                <th>نطاق السعر</th>
                                <th>المدة</th>
                                <th>الحالة</th>
                                <th>عدد الطلبات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for service in services %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="service-icon me-3" style="width: 40px; height: 40px; font-size: 1.2rem;">
                                            {% if service.category == 'installation' %}
                                                <i class="fas fa-home"></i>
                                            {% elif service.category == 'maintenance' %}
                                                <i class="fas fa-tools"></i>
                                            {% elif service.category == 'consultation' %}
                                                <i class="fas fa-user-tie"></i>
                                            {% elif service.category == 'training' %}
                                                <i class="fas fa-graduation-cap"></i>
                                            {% else %}
                                                <i class="fas fa-cogs"></i>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ service.name_ar }}</div>
                                            <small class="text-muted">{{ service.name_en }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ service.get_category_display }}</span>
                                </td>
                                <td>
                                    <span class="text-quantum">{{ service.price_range_display }}</span>
                                </td>
                                <td>{{ service.duration_hours }} ساعة</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        {% if service.is_active %}
                                            <span class="badge bg-success">نشطة</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشطة</span>
                                        {% endif %}
                                        
                                        {% if service.is_featured %}
                                            <span class="badge bg-warning text-dark">مميزة</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ service.requests.count }}</span>
                                </td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'company:admin_service_edit' service.id %}" 
                                           class="btn btn-sm btn-neon">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <button class="btn btn-sm btn-cyber" 
                                                onclick="toggleServiceStatus('{{ service.id }}', {{ service.is_active|yesno:'false,true' }})">
                                            {% if service.is_active %}
                                                <i class="fas fa-pause"></i>
                                            {% else %}
                                                <i class="fas fa-play"></i>
                                            {% endif %}
                                        </button>
                                        
                                        <a href="{% url 'company:service_detail' service.id %}" 
                                           class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <div class="text-muted">لا توجد خدمات متاحة</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row g-4 mt-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-number">{{ services.count }}</div>
                            <div class="stat-label">إجمالي الخدمات</div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number">{{ services|length }}</div>
                            <div class="stat-label">الخدمات النشطة</div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-number">{{ services|length }}</div>
                            <div class="stat-label">الخدمات المميزة</div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-number">0</div>
                            <div class="stat-label">إجمالي الطلبات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مودال إضافة خدمة جديدة -->
    <div class="modal fade" id="addServiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark border-cyber">
                <div class="modal-header border-cyber">
                    <h5 class="modal-title text-cyber">
                        <i class="fas fa-plus me-2"></i>إضافة خدمة جديدة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addServiceForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label-cyber">اسم الخدمة بالعربية</label>
                                <input type="text" class="form-control-cyber" name="name_ar" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-cyber">اسم الخدمة بالإنجليزية</label>
                                <input type="text" class="form-control-cyber" name="name_en" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label-cyber">فئة الخدمة</label>
                                <select class="form-control-cyber" name="category" required>
                                    <option value="">اختر الفئة</option>
                                    {% for value, label in categories %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-cyber">أقل سعر (د.ع)</label>
                                <input type="number" class="form-control-cyber" name="price_range_min" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-cyber">أعلى سعر (د.ع)</label>
                                <input type="number" class="form-control-cyber" name="price_range_max" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-cyber">مدة التنفيذ (ساعة)</label>
                                <input type="number" class="form-control-cyber" name="duration_hours" required>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="is_active" checked>
                                    <label class="form-check-label text-cyber">خدمة نشطة</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured">
                                    <label class="form-check-label text-cyber">خدمة مميزة</label>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label-cyber">وصف الخدمة بالعربية</label>
                                <textarea class="form-control-cyber" name="description_ar" rows="3" required></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label-cyber">وصف الخدمة بالإنجليزية</label>
                                <textarea class="form-control-cyber" name="description_en" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-cyber">
                    <button type="button" class="btn btn-neon" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-cyber" onclick="saveService()">
                        <i class="fas fa-save me-2"></i>حفظ الخدمة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تأثيرات التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal-cyber').forEach(el => {
            observer.observe(el);
        });
        
        // تبديل حالة الخدمة
        function toggleServiceStatus(serviceId, newStatus) {
            // هنا يمكن إضافة كود AJAX لتحديث حالة الخدمة
            console.log(`تبديل حالة الخدمة ${serviceId} إلى ${newStatus}`);
            
            // إظهار إشعار
            showNotification('تم تحديث حالة الخدمة بنجاح', 'success');
        }
        
        // حفظ خدمة جديدة
        function saveService() {
            const form = document.getElementById('addServiceForm');
            const formData = new FormData(form);
            
            // هنا يمكن إضافة كود AJAX لحفظ الخدمة
            console.log('حفظ خدمة جديدة...');
            
            // إغلاق المودال وإظهار إشعار
            const modal = bootstrap.Modal.getInstance(document.getElementById('addServiceModal'));
            modal.hide();
            
            showNotification('تم إضافة الخدمة بنجاح', 'success');
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
        
        // إشعارات النظام
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification-item ${type} show`;
            notification.innerHTML = `
                <button class="notification-close">&times;</button>
                <div class="notification-content">
                    <strong>${message}</strong>
                </div>
            `;
            
            // إضافة لوحة الإشعارات إذا لم تكن موجودة
            let panel = document.getElementById('notificationPanel');
            if (!panel) {
                panel = document.createElement('div');
                panel.id = 'notificationPanel';
                panel.className = 'notification-panel';
                document.body.appendChild(panel);
            }
            
            panel.appendChild(notification);
            
            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }, 5000);
            
            // إزالة عند النقر على زر الإغلاق
            notification.querySelector('.notification-close').addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            });
        }
    </script>
</body>
</html>
