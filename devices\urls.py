from django.urls import path
from . import views

app_name = 'devices'

urlpatterns = [
    # Device management
    path('devices/', views.DeviceListCreateAPIView.as_view(), name='device_list_create'),
    path('devices/<uuid:pk>/', views.DeviceDetailAPIView.as_view(), name='device_detail'),
    
    # Device data
    path('devices/<uuid:device_id>/data/', views.DeviceDataListCreateAPIView.as_view(), name='device_data'),
    path('device-data/', views.DeviceDataListCreateAPIView.as_view(), name='all_device_data'),
    
    # Control commands
    path('devices/<uuid:device_id>/commands/', views.ControlCommandListCreateAPIView.as_view(), name='device_commands'),
    path('commands/', views.ControlCommandListCreateAPIView.as_view(), name='all_commands'),
    path('commands/<int:pk>/', views.ControlCommandDetailAPIView.as_view(), name='command_detail'),
    
    # Statistics
    path('stats/', views.device_stats_api, name='device_stats'),
]
