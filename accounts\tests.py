from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from .models import User

User = get_user_model()


class UserModelTest(TestCase):
    """Test cases for User model"""

    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'phone': '+9647701234567'
        }

    def test_create_user(self):
        """Test creating a regular user"""
        user = User.objects.create_user(**self.user_data)

        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'user')
        self.assertFalse(user.is_admin)
        self.assertTrue(user.check_password('testpass123'))

    def test_create_admin_user(self):
        """Test creating an admin user"""
        admin_data = self.user_data.copy()
        admin_data['role'] = 'admin'
        admin_data['is_staff'] = True
        admin_data['is_superuser'] = True

        admin = User.objects.create_user(**admin_data)

        self.assertEqual(admin.role, 'admin')
        self.assertTrue(admin.is_admin)
        self.assertTrue(admin.is_staff)

    def test_user_str_method(self):
        """Test user string representation"""
        user = User.objects.create_user(**self.user_data)
        expected_str = f"{user.username} ({user.get_role_display()})"
        self.assertEqual(str(user), expected_str)

    def test_full_name_property(self):
        """Test full_name property"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.full_name, "Test User")

        # Test with no first/last name
        user.first_name = ""
        user.last_name = ""
        user.save()
        self.assertEqual(user.full_name, "testuser")
