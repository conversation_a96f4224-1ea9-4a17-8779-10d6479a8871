"""
Context processors للشركة
"""

from .models import SiteSettings


def site_settings(request):
    """
    إضافة إعدادات الموقع لجميع القوالب
    """
    try:
        settings = SiteSettings.get_settings()
        return {
            'site_settings': settings,
            'company_name': settings.company_name_ar,
            'company_slogan': settings.company_slogan_ar,
            'phone_primary': settings.phone_primary,
            'email_primary': settings.email_primary,
            'address_ar': settings.address_ar,
            'working_hours_ar': settings.working_hours_ar,
        }
    except Exception:
        # في حالة عدم وجود إعدادات، إرجاع قيم افتراضية
        return {
            'site_settings': None,
            'company_name': 'IQHome',
            'company_slogan': 'الشركة الرائدة في تقنيات المنازل الذكية في العراق',
            'phone_primary': '+*********** 4567',
            'email_primary': '<EMAIL>',
            'address_ar': 'بغداد - الكرادة',
            'working_hours_ar': 'السبت - الخميس: 9:00 ص - 6:00 م',
        }
