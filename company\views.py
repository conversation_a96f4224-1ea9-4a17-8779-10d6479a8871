from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
from .models import Service, ServiceRequest, ContactMessage, Portfolio, Testimonial, FAQ


def home_view(request):
    """
    الصفحة الرئيسية للشركة
    """
    featured_services = Service.objects.filter(is_active=True, is_featured=True)[:3]
    all_services = Service.objects.filter(is_active=True)[:6]

    context = {
        'featured_services': featured_services,
        'all_services': all_services,
    }
    return render(request, 'simple_home.html', context)


def services_view(request):
    """
    عرض صفحة الخدمات
    """
    services = Service.objects.filter(is_active=True).order_by('-is_featured', 'name_ar')
    featured_services = services.filter(is_featured=True)
    featured_count = featured_services.count()

    context = {
        'services': services,
        'featured_services': featured_services,
        'featured_count': featured_count,
    }
    return render(request, 'company/services_dark.html', context)


def service_detail_view(request, service_id):
    """
    عرض تفاصيل خدمة معينة
    """
    try:
        service = Service.objects.get(id=service_id, is_active=True)
        related_services = Service.objects.filter(
            category=service.category,
            is_active=True
        ).exclude(id=service_id)[:3]

        context = {
            'service': service,
            'related_services': related_services,
        }
        return render(request, 'company/service_detail.html', context)
    except Service.DoesNotExist:
        messages.error(request, 'الخدمة المطلوبة غير موجودة')
        return redirect('company:services')


def request_service_view(request):
    """
    صفحة طلب الخدمة
    """
    if request.method == 'POST':
        try:
            # استخراج البيانات من النموذج
            service_id = request.POST.get('service')
            customer_name = request.POST.get('customer_name')
            customer_phone = request.POST.get('customer_phone')
            customer_email = request.POST.get('customer_email')
            customer_address = request.POST.get('customer_address')
            customer_city = request.POST.get('customer_city')
            description = request.POST.get('description')
            preferred_date = request.POST.get('preferred_date')
            preferred_time = request.POST.get('preferred_time')
            customer_notes = request.POST.get('customer_notes', '')

            # التحقق من البيانات المطلوبة
            if not all([service_id, customer_name, customer_phone, customer_city, description]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return render(request, 'company/request_service_dark.html', {'services': Service.objects.filter(is_active=True)})

            # إنشاء طلب الخدمة
            service = Service.objects.get(id=service_id)
            service_request = ServiceRequest.objects.create(
                service=service,
                customer_name=customer_name,
                customer_phone=customer_phone,
                customer_email=customer_email or '',
                customer_address=customer_address or '',
                customer_city=customer_city,
                description=description,
                preferred_date=preferred_date,
                preferred_time=preferred_time,
                customer_notes=customer_notes
            )

            messages.success(request, f'تم إرسال طلبك بنجاح! رقم الطلب: {service_request.request_number}')
            return redirect('company:request_success', request_id=service_request.id)

        except Service.DoesNotExist:
            messages.error(request, 'الخدمة المحددة غير موجودة')
        except Exception as e:
            messages.error(request, 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى')

    services = Service.objects.filter(is_active=True)
    return render(request, 'company/request_service_dark.html', {'services': services})


def request_success_view(request, request_id):
    """
    صفحة نجاح إرسال الطلب
    """
    try:
        service_request = ServiceRequest.objects.get(id=request_id)
        return render(request, 'company/request_success.html', {'request': service_request})
    except ServiceRequest.DoesNotExist:
        messages.error(request, 'الطلب غير موجود')
        return redirect('company:home')


def contact_view(request):
    """
    صفحة التواصل
    """
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            email = request.POST.get('email')
            phone = request.POST.get('phone')
            subject = request.POST.get('subject')
            message = request.POST.get('message')
            message_type = request.POST.get('message_type', 'inquiry')

            if not all([name, email, subject, message]):
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
                return render(request, 'company/contact_dark.html')

            ContactMessage.objects.create(
                name=name,
                email=email,
                phone=phone or '',
                subject=subject,
                message=message,
                message_type=message_type
            )

            messages.success(request, 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً')
            return redirect('company:contact')

        except Exception as e:
            messages.error(request, 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى')

    return render(request, 'company/contact_dark.html')


def portfolio_view(request):
    """
    عرض معرض الأعمال
    """
    projects = Portfolio.objects.filter(is_published=True).prefetch_related('images', 'service').order_by('-display_order', '-created_at')

    # إحصائيات المعرض
    total_projects = projects.count()
    featured_projects = projects.filter(is_featured=True).count()
    video_projects = projects.filter(media_type='video').count()
    gallery_projects = projects.filter(media_type='gallery').count()

    context = {
        'projects': projects,
        'total_projects': total_projects,
        'featured_projects': featured_projects,
        'video_projects': video_projects,
        'gallery_projects': gallery_projects,
    }

    return render(request, 'company/portfolio_dark.html', context)


def portfolio_detail_view(request, project_id):
    """
    عرض تفاصيل مشروع معين
    """
    project = get_object_or_404(Portfolio, id=project_id, is_published=True)
    related_projects = Portfolio.objects.filter(
        category=project.category,
        is_published=True
    ).exclude(id=project.id)[:3]

    context = {
        'project': project,
        'related_projects': related_projects,
    }

    return render(request, 'company/portfolio_detail.html', context)


@csrf_exempt
def ajax_request_service(request):
    """
    معالجة طلب الخدمة عبر AJAX
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            # التحقق من البيانات المطلوبة
            required_fields = ['service_type', 'customer_name', 'customer_phone', 'customer_city']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({
                        'success': False,
                        'message': f'الحقل {field} مطلوب'
                    })

            # إنشاء طلب خدمة مبسط
            service_request = ServiceRequest.objects.create(
                service_id=data.get('service_id') if data.get('service_id') else None,
                customer_name=data['customer_name'],
                customer_phone=data['customer_phone'],
                customer_email=data.get('customer_email', ''),
                customer_address=data.get('customer_address', ''),
                customer_city=data['customer_city'],
                description=data.get('description', f"طلب خدمة: {data.get('service_type', 'غير محدد')}"),
                preferred_date=data.get('preferred_date'),
                preferred_time=data.get('preferred_time'),
                customer_notes=data.get('customer_notes', '')
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إرسال طلبك بنجاح!',
                'request_number': service_request.request_number
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': 'خطأ في تنسيق البيانات'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': 'حدث خطأ أثناء معالجة الطلب'
            })

    return JsonResponse({
        'success': False,
        'message': 'طريقة الطلب غير مدعومة'
    })
