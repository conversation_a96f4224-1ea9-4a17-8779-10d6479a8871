# Generated by Django 5.2.4 on 2025-07-29 03:19

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name_ar', models.CharField(max_length=200, verbose_name='اسم الخدمة بالعربية')),
                ('name_en', models.CharField(max_length=200, verbose_name='اسم الخدمة بالإنجليزية')),
                ('description_ar', models.TextField(verbose_name='وصف الخدمة بالعربية')),
                ('description_en', models.TextField(verbose_name='وصف الخدمة بالإنجليزية')),
                ('category', models.CharField(choices=[('installation', 'تركيب المنازل الذكية'), ('maintenance', 'صيانة وإصلاح'), ('consultation', 'استشارات تقنية'), ('upgrade', 'ترقية الأنظمة'), ('monitoring', 'خدمات المراقبة'), ('training', 'التدريب والتأهيل')], max_length=20, verbose_name='فئة الخدمة')),
                ('price_range_min', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='أقل سعر')),
                ('price_range_max', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='أعلى سعر')),
                ('duration_hours', models.IntegerField(verbose_name='مدة التنفيذ بالساعات')),
                ('main_image', models.ImageField(upload_to='services/', verbose_name='الصورة الرئيسية')),
                ('gallery_images', models.JSONField(blank=True, default=list, verbose_name='معرض الصور')),
                ('features', models.JSONField(default=list, verbose_name='الميزات')),
                ('requirements', models.JSONField(default=list, verbose_name='المتطلبات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('is_featured', models.BooleanField(default=False, verbose_name='خدمة مميزة')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'خدمة',
                'verbose_name_plural': 'الخدمات',
                'db_table': 'services',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('message_type', models.CharField(choices=[('inquiry', 'استفسار عام'), ('quote', 'طلب عرض سعر'), ('complaint', 'شكوى'), ('suggestion', 'اقتراح'), ('partnership', 'شراكة')], default='inquiry', max_length=20, verbose_name='نوع الرسالة')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('is_replied', models.BooleanField(default=False, verbose_name='تم الرد')),
                ('reply_message', models.TextField(blank=True, verbose_name='رسالة الرد')),
                ('replied_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الرد')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإرسال')),
                ('replied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الرد بواسطة')),
            ],
            options={
                'verbose_name': 'رسالة تواصل',
                'verbose_name_plural': 'رسائل التواصل',
                'db_table': 'contact_messages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('request_number', models.CharField(editable=False, max_length=20, unique=True, verbose_name='رقم الطلب')),
                ('customer_name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('customer_phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('customer_email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('customer_address', models.TextField(verbose_name='العنوان')),
                ('customer_city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('description', models.TextField(verbose_name='وصف الطلب')),
                ('preferred_date', models.DateField(verbose_name='التاريخ المفضل')),
                ('preferred_time', models.TimeField(verbose_name='الوقت المفضل')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('reviewing', 'قيد المراجعة'), ('approved', 'موافق عليه'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة المقدرة')),
                ('final_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة النهائية')),
                ('customer_notes', models.TextField(blank=True, verbose_name='ملاحظات العميل')),
                ('admin_notes', models.TextField(blank=True, verbose_name='ملاحظات الإدارة')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_requests', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='company.service', verbose_name='الخدمة')),
            ],
            options={
                'verbose_name': 'طلب خدمة',
                'verbose_name_plural': 'طلبات الخدمات',
                'db_table': 'service_requests',
                'ordering': ['-created_at'],
            },
        ),
    ]
