/* IQHome Smart Home Platform - Custom Styles */

:root {
    --primary-color: #007BFF;
    --primary-dark: #0056b3;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    
    /* Custom colors for IQHome */
    --electric-blue: #007BFF;
    --smart-green: #28a745;
    --tech-gray: #6c757d;
    --alert-red: #dc3545;
    --warning-amber: #ffc107;
    
    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #007BFF, #0056b3);
    --success-gradient: linear-gradient(135deg, #28a745, #1e7e34);
    --warning-gradient: linear-gradient(135deg, #ffc107, #e0a800);
    --danger-gradient: linear-gradient(135deg, #dc3545, #c82333);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.25);
    
    /* Border radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 50%;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-electric-blue {
    color: var(--electric-blue) !important;
}

.text-smart-green {
    color: var(--smart-green) !important;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--transition-normal);
    border: none;
    padding: 0.5rem 1.5rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--white);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--dark-color);
}

.btn-danger {
    background: var(--danger-gradient);
    color: var(--white);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Cards */
.card {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background: var(--white);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 600;
}

/* Dashboard Cards */
.dashboard-card {
    transition: transform var(--transition-normal);
    cursor: pointer;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.dashboard-card .card-body {
    padding: 2rem;
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Device Cards */
.device-card {
    position: relative;
    transition: all var(--transition-normal);
    cursor: pointer;
    min-height: 180px;
}

.device-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.device-status {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 12px;
    height: 12px;
    border-radius: var(--border-radius-full);
    border: 2px solid var(--white);
    box-shadow: var(--shadow-sm);
}

.device-status.online {
    background-color: var(--success-color);
    animation: pulse-green 2s infinite;
}

.device-status.offline {
    background-color: var(--danger-color);
}

.device-status.error {
    background-color: var(--warning-color);
    animation: pulse-yellow 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-yellow {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* Forms */
.form-control {
    border-radius: var(--border-radius-md);
    border: 1px solid #dee2e6;
    transition: all var(--transition-fast);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow-sm);
    background: var(--white) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color var(--transition-fast);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius-md);
    border: none;
    box-shadow: var(--shadow-sm);
}

.alert-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Tables */
.table {
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.table th {
    font-weight: 600;
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badges */
.badge {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
    
    .stat-icon {
        font-size: 2rem;
    }
    
    .device-card {
        min-height: 150px;
    }
}

/* Utility Classes */
.shadow-custom {
    box-shadow: var(--shadow-md);
}

.rounded-custom {
    border-radius: var(--border-radius-lg);
}

.gradient-primary {
    background: var(--primary-gradient);
}

.gradient-success {
    background: var(--success-gradient);
}

.gradient-warning {
    background: var(--warning-gradient);
}

.gradient-danger {
    background: var(--danger-gradient);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 3rem 0 2rem;
    margin-top: auto;
}

.footer h5 {
    color: var(--white);
    font-weight: 600;
}

.footer p {
    color: rgba(255, 255, 255, 0.8);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding-top: 2rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--tech-gray);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
