from django.core.management.base import BaseCommand
from company.models import Service


class Command(BaseCommand):
    help = 'إنشاء خدمات تجريبية لشركة IQHome'

    def handle(self, *args, **options):
        self.stdout.write('إنشاء خدمات الشركة...')
        
        services_data = [
            {
                'name_ar': 'تركيب منزل ذكي كامل',
                'name_en': 'Complete Smart Home Installation',
                'description_ar': 'تركيب وتكوين نظام منزل ذكي متكامل يشمل الإضاءة الذكية، أنظمة الأمان، التحكم في المناخ، وإدارة الطاقة. نقدم حلول شاملة تناسب جميع أحجام المنازل.',
                'description_en': 'Complete smart home system installation including smart lighting, security systems, climate control, and energy management.',
                'category': 'installation',
                'price_range_min': 5000000,
                'price_range_max': 25000000,
                'duration_hours': 48,
                'features': [
                    'إضاءة ذكية في جميع الغرف',
                    'نظام أمان متطور مع كاميرات',
                    'تحكم في المناخ والتكييف',
                    'إدارة ذكية للطاقة',
                    'تطبيق موبايل للتحكم',
                    'ضمان سنتين'
                ],
                'requirements': [
                    'شبكة إنترنت مستقرة',
                    'تمديدات كهربائية حديثة',
                    'موافقة مالك العقار'
                ],
                'is_featured': True
            },
            {
                'name_ar': 'صيانة وإصلاح الأنظمة الذكية',
                'name_en': 'Smart Systems Maintenance & Repair',
                'description_ar': 'خدمات صيانة دورية وإصلاح فوري لجميع أنواع الأنظمة الذكية. فريقنا المتخصص متاح 24/7 لضمان عمل أنظمتك بأفضل أداء.',
                'description_en': 'Regular maintenance and immediate repair services for all types of smart systems.',
                'category': 'maintenance',
                'price_range_min': 100000,
                'price_range_max': 2000000,
                'duration_hours': 4,
                'features': [
                    'صيانة دورية شهرية',
                    'إصلاح فوري للأعطال',
                    'تحديث البرمجيات',
                    'فحص شامل للأنظمة',
                    'دعم فني 24/7',
                    'قطع غيار أصلية'
                ],
                'requirements': [
                    'عقد صيانة ساري',
                    'إمكانية الوصول للأنظمة'
                ],
                'is_featured': True
            },
            {
                'name_ar': 'استشارات تقنية متخصصة',
                'name_en': 'Technical Consultation Services',
                'description_ar': 'استشارات تقنية من خبراء متخصصين لمساعدتك في اختيار أفضل الحلول التقنية المناسبة لاحتياجاتك وميزانيتك.',
                'description_en': 'Expert technical consultations to help you choose the best technology solutions.',
                'category': 'consultation',
                'price_range_min': 200000,
                'price_range_max': 1000000,
                'duration_hours': 2,
                'features': [
                    'تقييم شامل للمنزل',
                    'خطة تنفيذ مفصلة',
                    'توصيات المنتجات',
                    'تقدير التكاليف',
                    'جدولة التنفيذ',
                    'متابعة لمدة شهر'
                ],
                'requirements': [
                    'موعد مسبق',
                    'مخططات المنزل (إن وجدت)'
                ],
                'is_featured': False
            },
            {
                'name_ar': 'أنظمة الأمان والمراقبة',
                'name_en': 'Security & Surveillance Systems',
                'description_ar': 'تركيب أنظمة أمان متطورة تشمل كاميرات مراقبة عالية الدقة، أجهزة إنذار ذكية، وأنظمة التحكم في الدخول.',
                'description_en': 'Advanced security systems including HD cameras, smart alarms, and access control.',
                'category': 'installation',
                'price_range_min': 2000000,
                'price_range_max': 10000000,
                'duration_hours': 16,
                'features': [
                    'كاميرات عالية الدقة',
                    'تسجيل مستمر',
                    'إنذارات ذكية',
                    'تحكم في الدخول',
                    'تطبيق مراقبة',
                    'تخزين سحابي'
                ],
                'requirements': [
                    'شبكة إنترنت قوية',
                    'نقاط كهرباء كافية',
                    'تصاريح التركيب'
                ],
                'is_featured': True
            },
            {
                'name_ar': 'إدارة الطاقة الذكية',
                'name_en': 'Smart Energy Management',
                'description_ar': 'حلول ذكية لإدارة استهلاك الطاقة وتوفير الكهرباء مع أنظمة مراقبة متطورة وتحكم آلي في الأجهزة.',
                'description_en': 'Smart solutions for energy management and electricity savings.',
                'category': 'installation',
                'price_range_min': 1500000,
                'price_range_max': 8000000,
                'duration_hours': 12,
                'features': [
                    'مراقبة الاستهلاك',
                    'تحكم آلي في الأجهزة',
                    'تقارير توفير الطاقة',
                    'جدولة التشغيل',
                    'تنبيهات الاستهلاك',
                    'تحسين الفواتير'
                ],
                'requirements': [
                    'عداد كهرباء ذكي',
                    'أجهزة قابلة للتحكم'
                ],
                'is_featured': False
            },
            {
                'name_ar': 'التدريب والتأهيل',
                'name_en': 'Training & Education',
                'description_ar': 'برامج تدريبية شاملة لتعليم العملاء كيفية استخدام وإدارة أنظمة المنازل الذكية بفعالية وأمان.',
                'description_en': 'Comprehensive training programs for smart home system usage.',
                'category': 'training',
                'price_range_min': 300000,
                'price_range_max': 1500000,
                'duration_hours': 8,
                'features': [
                    'تدريب شخصي',
                    'دليل استخدام مفصل',
                    'جلسات متابعة',
                    'دعم هاتفي',
                    'فيديوهات تعليمية',
                    'شهادة إتمام'
                ],
                'requirements': [
                    'نظام ذكي مركب',
                    'جهاز ذكي للتدريب'
                ],
                'is_featured': False
            }
        ]
        
        created_count = 0
        for service_data in services_data:
            service, created = Service.objects.get_or_create(
                name_ar=service_data['name_ar'],
                defaults=service_data
            )
            if created:
                created_count += 1
                self.stdout.write(f'تم إنشاء الخدمة: {service.name_ar}')
            else:
                self.stdout.write(f'الخدمة موجودة مسبقاً: {service.name_ar}')
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} خدمة جديدة بنجاح!')
        )
