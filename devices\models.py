from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class Device(models.Model):
    """
    Smart device model for IQHome platform
    Represents physical smart devices connected to the system
    """

    DEVICE_TYPES = [
        ('light', 'Smart Light'),
        ('switch', 'Smart Switch'),
        ('sensor', 'Sensor'),
        ('camera', 'Security Camera'),
        ('thermostat', 'Thermostat'),
        ('lock', 'Smart Lock'),
        ('outlet', 'Smart Outlet'),
        ('water', 'Water Controller'),
        ('energy', 'Energy Monitor'),
        ('other', 'Other Device'),
    ]

    STATUS_CHOICES = [
        ('online', 'Online'),
        ('offline', 'Offline'),
        ('error', 'Error'),
        ('maintenance', 'Maintenance'),
    ]

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    device_type = models.CharField(max_length=20, choices=DEVICE_TYPES)

    # Network identification
    mac_address = models.CharField(max_length=17, unique=True)  # MAC address format: XX:XX:XX:XX:XX:XX
    ip_address = models.GenericIPAddressField(blank=True, null=True)

    # Device status and configuration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='offline')
    is_active = models.BooleanField(default=True)

    # Location and ownership
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='devices')
    location = models.CharField(max_length=100, blank=True, null=True)  # Room/area name

    # Device specifications
    model_number = models.CharField(max_length=50, blank=True, null=True)
    firmware_version = models.CharField(max_length=20, blank=True, null=True)
    manufacturer = models.CharField(max_length=50, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    last_seen = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'devices'
        verbose_name = 'Device'
        verbose_name_plural = 'Devices'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_device_type_display()}) - {self.user.username}"

    @property
    def is_online(self):
        """Check if device is currently online"""
        return self.status == 'online'

    def update_last_seen(self):
        """Update the last seen timestamp"""
        self.last_seen = timezone.now()
        self.save(update_fields=['last_seen'])


class DeviceData(models.Model):
    """
    Model to store time-series data from smart devices
    Stores sensor readings, status updates, and other device metrics
    """

    device = models.ForeignKey(Device, on_delete=models.CASCADE, related_name='data_points')
    timestamp = models.DateTimeField(default=timezone.now)
    key = models.CharField(max_length=50)  # e.g., 'temperature', 'humidity', 'power_consumption'
    value = models.TextField()  # Store as string, can be parsed based on key
    unit = models.CharField(max_length=20, blank=True, null=True)  # e.g., '°C', '%', 'kWh'

    class Meta:
        db_table = 'device_data'
        verbose_name = 'Device Data'
        verbose_name_plural = 'Device Data'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['device', 'key', '-timestamp']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.device.name} - {self.key}: {self.value} {self.unit or ''}"


class ControlCommand(models.Model):
    """
    Model to track commands sent to smart devices
    Provides audit trail for device control actions
    """

    COMMAND_TYPES = [
        ('turn_on', 'Turn On'),
        ('turn_off', 'Turn Off'),
        ('set_brightness', 'Set Brightness'),
        ('set_temperature', 'Set Temperature'),
        ('lock', 'Lock'),
        ('unlock', 'Unlock'),
        ('open', 'Open'),
        ('close', 'Close'),
        ('reset', 'Reset'),
        ('update_firmware', 'Update Firmware'),
        ('custom', 'Custom Command'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('acknowledged', 'Acknowledged'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('timeout', 'Timeout'),
    ]

    device = models.ForeignKey(Device, on_delete=models.CASCADE, related_name='commands')
    command_type = models.CharField(max_length=20, choices=COMMAND_TYPES)
    command_data = models.JSONField(blank=True, null=True)  # Additional command parameters
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # User who issued the command
    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='issued_commands')

    # Timestamps
    timestamp = models.DateTimeField(default=timezone.now)
    sent_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    # Response from device
    response_data = models.JSONField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'control_commands'
        verbose_name = 'Control Command'
        verbose_name_plural = 'Control Commands'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.get_command_type_display()} - {self.device.name} by {self.issued_by.username}"

    def mark_as_sent(self):
        """Mark command as sent"""
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.save(update_fields=['status', 'sent_at'])

    def mark_as_completed(self, response_data=None):
        """Mark command as completed"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        if response_data:
            self.response_data = response_data
        self.save(update_fields=['status', 'completed_at', 'response_data'])

    def mark_as_failed(self, error_message=None):
        """Mark command as failed"""
        self.status = 'failed'
        self.completed_at = timezone.now()
        if error_message:
            self.error_message = error_message
        self.save(update_fields=['status', 'completed_at', 'error_message'])
