{% extends 'base.html' %}

{% block title %}Admin Login - IQHome{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card border-warning">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                    <h3 class="card-title">Admin Portal</h3>
                    <p class="text-muted">Administrator access only</p>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user-shield me-2"></i>Admin Username
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-key me-2"></i>Admin Password
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-warning w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Admin Sign In
                    </button>
                </form>
                
                <div class="text-center">
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-arrow-left me-2"></i>Back to User Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.fa-shield-alt {
    color: var(--warning-color);
}

.btn-warning {
    color: #212529;
    font-weight: 500;
}

.btn-warning:hover {
    transform: translateY(-2px);
    color: #212529;
}
</style>
{% endblock %}
