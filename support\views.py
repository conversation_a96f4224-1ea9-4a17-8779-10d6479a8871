from django.shortcuts import render
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Q
from .models import SupportTicket, TicketMessage
from .serializers import (
    SupportTicketSerializer, SupportTicketCreateSerializer,
    SupportTicketUpdateSerializer, TicketMessageSerializer,
    TicketMessageCreateSerializer, SupportTicketDetailSerializer,
    SupportStatsSerializer, AdminTicketListSerializer
)


class SupportTicketListCreateAPIView(generics.ListCreateAPIView):
    """
    List support tickets for the authenticated user or create a new ticket
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin:
            return SupportTicket.objects.all().select_related('user', 'assigned_to', 'device_related')
        return SupportTicket.objects.filter(user=user).select_related('user', 'assigned_to', 'device_related')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SupportTicketCreateSerializer
        elif self.request.user.is_admin:
            return AdminTicketListSerializer
        return SupportTicketSerializer


class SupportTicketDetailAPIView(generics.RetrieveUpdateAPIView):
    """
    Retrieve or update a support ticket
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_admin:
            return SupportTicket.objects.all().select_related('user', 'assigned_to', 'device_related')
        return SupportTicket.objects.filter(user=user).select_related('user', 'assigned_to', 'device_related')

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH'] and self.request.user.is_admin:
            return SupportTicketUpdateSerializer
        return SupportTicketDetailSerializer


class TicketMessageListCreateAPIView(generics.ListCreateAPIView):
    """
    List messages for a ticket or create a new message
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        ticket_id = self.kwargs.get('ticket_id')

        # Get the ticket first to check permissions
        try:
            if user.is_admin:
                ticket = SupportTicket.objects.get(id=ticket_id)
            else:
                ticket = SupportTicket.objects.get(id=ticket_id, user=user)
        except SupportTicket.DoesNotExist:
            return TicketMessage.objects.none()

        messages = ticket.messages.all()

        # Filter out internal messages for non-admin users
        if not user.is_admin:
            messages = messages.filter(is_internal=False)

        return messages.select_related('sender').order_by('created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TicketMessageCreateSerializer
        return TicketMessageSerializer


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def close_ticket_api(request, ticket_id):
    """
    Close a support ticket
    """
    user = request.user

    try:
        if user.is_admin:
            ticket = SupportTicket.objects.get(id=ticket_id)
        else:
            ticket = SupportTicket.objects.get(id=ticket_id, user=user)
    except SupportTicket.DoesNotExist:
        return Response({'error': 'Ticket not found'}, status=status.HTTP_404_NOT_FOUND)

    resolved = request.data.get('resolved', True)
    ticket.close_ticket(resolved=resolved)

    return Response({
        'message': 'Ticket closed successfully',
        'ticket_number': ticket.ticket_number,
        'status': ticket.status
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def support_stats_api(request):
    """
    Get support statistics
    """
    user = request.user

    if user.is_admin:
        tickets = SupportTicket.objects.all()
    else:
        tickets = SupportTicket.objects.filter(user=user)

    total_tickets = tickets.count()
    open_tickets = tickets.filter(status__in=['open', 'in_progress', 'waiting_user']).count()
    resolved_tickets = tickets.filter(status='resolved').count()
    closed_tickets = tickets.filter(status='closed').count()

    # Tickets by category
    tickets_by_category = tickets.values('category').annotate(
        count=Count('category')
    ).order_by('-count')
    category_dict = {item['category']: item['count'] for item in tickets_by_category}

    # Tickets by priority
    tickets_by_priority = tickets.values('priority').annotate(
        count=Count('priority')
    ).order_by('-count')
    priority_dict = {item['priority']: item['count'] for item in tickets_by_priority}

    # Recent tickets
    recent_tickets = tickets.order_by('-created_at')[:10]
    recent_tickets_data = [
        {
            'ticket_number': ticket.ticket_number,
            'title': ticket.title,
            'status': ticket.status,
            'priority': ticket.priority,
            'created_at': ticket.created_at,
            'user': ticket.user.username
        }
        for ticket in recent_tickets
    ]

    stats_data = {
        'total_tickets': total_tickets,
        'open_tickets': open_tickets,
        'resolved_tickets': resolved_tickets,
        'closed_tickets': closed_tickets,
        'tickets_by_category': category_dict,
        'tickets_by_priority': priority_dict,
        'recent_tickets': recent_tickets_data
    }

    serializer = SupportStatsSerializer(stats_data)
    return Response(serializer.data)
