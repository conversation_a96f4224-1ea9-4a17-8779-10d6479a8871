/**
 * IQHome Smart Home Platform - JavaScript Functions
 * Main JavaScript file for IQHome platform functionality
 */

// Global configuration
const IQHOME = {
    API_BASE_URL: '/api/',
    REFRESH_INTERVAL: 30000, // 30 seconds
    CHART_COLORS: {
        primary: '#007BFF',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        secondary: '#6c757d'
    }
};

// Utility functions
const Utils = {
    /**
     * Format timestamp to readable format
     */
    formatTimestamp: function(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Less than 1 minute
            return 'Just now';
        } else if (diff < 3600000) { // Less than 1 hour
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diff < 86400000) { // Less than 1 day
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    },

    /**
     * Show loading state on element
     */
    showLoading: function(element) {
        element.classList.add('loading');
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border spinner-border-sm me-2';
        spinner.setAttribute('role', 'status');
        element.prepend(spinner);
    },

    /**
     * Hide loading state from element
     */
    hideLoading: function(element) {
        element.classList.remove('loading');
        const spinner = element.querySelector('.spinner-border');
        if (spinner) {
            spinner.remove();
        }
    },

    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    },

    /**
     * Create toast container if it doesn't exist
     */
    createToastContainer: function() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    },

    /**
     * Format device status
     */
    formatDeviceStatus: function(status) {
        const statusMap = {
            'online': { text: 'Online', class: 'success' },
            'offline': { text: 'Offline', class: 'danger' },
            'error': { text: 'Error', class: 'warning' },
            'maintenance': { text: 'Maintenance', class: 'secondary' }
        };
        return statusMap[status] || { text: status, class: 'secondary' };
    },

    /**
     * Get CSRF token
     */
    getCSRFToken: function() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
};

// API functions
const API = {
    /**
     * Make API request
     */
    request: async function(endpoint, options = {}) {
        const url = IQHOME.API_BASE_URL + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': Utils.getCSRFToken()
            }
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            Utils.showToast('API request failed: ' + error.message, 'danger');
            throw error;
        }
    },

    /**
     * Get devices
     */
    getDevices: async function() {
        return await this.request('devices/');
    },

    /**
     * Get device details
     */
    getDevice: async function(deviceId) {
        return await this.request(`devices/${deviceId}/`);
    },

    /**
     * Control device
     */
    controlDevice: async function(deviceId, command, data = {}) {
        return await this.request(`devices/${deviceId}/commands/`, {
            method: 'POST',
            body: JSON.stringify({
                command_type: command,
                command_data: data
            })
        });
    },

    /**
     * Get notifications
     */
    getNotifications: async function() {
        return await this.request('notifications/');
    },

    /**
     * Mark notification as read
     */
    markNotificationRead: async function(notificationId) {
        return await this.request(`notifications/${notificationId}/read/`, {
            method: 'POST'
        });
    },

    /**
     * Get dashboard stats
     */
    getDashboardStats: async function() {
        return await this.request('admin/dashboard-stats/');
    }
};

// Device management functions
const DeviceManager = {
    /**
     * Load and display devices
     */
    loadDevices: async function(containerId = 'devices-grid') {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            Utils.showLoading(container);
            const devices = await API.getDevices();
            this.renderDevices(devices.results || devices, container);
        } catch (error) {
            container.innerHTML = '<div class="col-12 text-center text-danger">Failed to load devices</div>';
        } finally {
            Utils.hideLoading(container);
        }
    },

    /**
     * Render devices in grid
     */
    renderDevices: function(devices, container) {
        if (!devices || devices.length === 0) {
            container.innerHTML = '<div class="col-12 text-center text-muted">No devices found</div>';
            return;
        }

        container.innerHTML = devices.map(device => this.createDeviceCard(device)).join('');
    },

    /**
     * Create device card HTML
     */
    createDeviceCard: function(device) {
        const status = Utils.formatDeviceStatus(device.status);
        const icon = this.getDeviceIcon(device.device_type);
        
        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card device-card" onclick="DeviceManager.toggleDevice('${device.id}')">
                    <div class="device-status ${device.status}"></div>
                    <div class="card-body text-center">
                        <i class="${icon} fa-2x mb-2"></i>
                        <h6>${device.name}</h6>
                        <small class="text-muted">${device.location || 'No location'}</small>
                        <div class="device-control mt-2" style="display: none;">
                            <button class="btn btn-sm btn-outline-primary" onclick="DeviceManager.controlDevice('${device.id}', 'toggle')">
                                Toggle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * Get device icon based on type
     */
    getDeviceIcon: function(deviceType) {
        const iconMap = {
            'light': 'fas fa-lightbulb text-warning',
            'switch': 'fas fa-toggle-on text-primary',
            'sensor': 'fas fa-thermometer-half text-info',
            'camera': 'fas fa-video text-secondary',
            'thermostat': 'fas fa-temperature-high text-danger',
            'lock': 'fas fa-lock text-dark',
            'outlet': 'fas fa-plug text-success',
            'water': 'fas fa-tint text-info',
            'energy': 'fas fa-bolt text-warning'
        };
        return iconMap[deviceType] || 'fas fa-microchip text-secondary';
    },

    /**
     * Toggle device expanded state
     */
    toggleDevice: function(deviceId) {
        const card = event.currentTarget;
        card.classList.toggle('expanded');
        
        const controls = card.querySelector('.device-control');
        if (controls) {
            controls.style.display = card.classList.contains('expanded') ? 'block' : 'none';
        }
    },

    /**
     * Control device
     */
    controlDevice: async function(deviceId, command) {
        event.stopPropagation(); // Prevent card toggle
        
        try {
            await API.controlDevice(deviceId, command);
            Utils.showToast('Device command sent successfully', 'success');
            
            // Refresh devices after a short delay
            setTimeout(() => this.loadDevices(), 1000);
        } catch (error) {
            Utils.showToast('Failed to control device', 'danger');
        }
    }
};

// Notification management
const NotificationManager = {
    /**
     * Load and display notifications
     */
    loadNotifications: async function(containerId = 'notifications-list') {
        const container = document.getElementById(containerId);
        if (!container) return;

        try {
            const notifications = await API.getNotifications();
            this.renderNotifications(notifications.results || notifications, container);
        } catch (error) {
            container.innerHTML = '<p class="text-danger">Failed to load notifications</p>';
        }
    },

    /**
     * Render notifications
     */
    renderNotifications: function(notifications, container) {
        if (!notifications || notifications.length === 0) {
            container.innerHTML = '<p class="text-muted">No new notifications</p>';
            return;
        }

        container.innerHTML = notifications.slice(0, 5).map(notification => 
            this.createNotificationItem(notification)
        ).join('');
    },

    /**
     * Create notification item HTML
     */
    createNotificationItem: function(notification) {
        const typeClass = this.getNotificationClass(notification.notification_type);
        const timeAgo = Utils.formatTimestamp(notification.created_at);
        
        return `
            <div class="alert alert-${typeClass} alert-sm" onclick="NotificationManager.markAsRead('${notification.id}')">
                <i class="${this.getNotificationIcon(notification.notification_type)} me-2"></i>
                ${notification.title}
                <small class="d-block text-muted">${timeAgo}</small>
            </div>
        `;
    },

    /**
     * Get notification CSS class
     */
    getNotificationClass: function(type) {
        const classMap = {
            'info': 'info',
            'warning': 'warning',
            'error': 'danger',
            'success': 'success',
            'device_alert': 'warning',
            'system': 'secondary'
        };
        return classMap[type] || 'info';
    },

    /**
     * Get notification icon
     */
    getNotificationIcon: function(type) {
        const iconMap = {
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
            'success': 'fas fa-check-circle',
            'device_alert': 'fas fa-microchip',
            'system': 'fas fa-cog'
        };
        return iconMap[type] || 'fas fa-bell';
    },

    /**
     * Mark notification as read
     */
    markAsRead: async function(notificationId) {
        try {
            await API.markNotificationRead(notificationId);
            Utils.showToast('Notification marked as read', 'success');
            this.loadNotifications();
        } catch (error) {
            Utils.showToast('Failed to mark notification as read', 'danger');
        }
    }
};

// Dashboard functions
const Dashboard = {
    /**
     * Initialize dashboard
     */
    init: function() {
        this.loadStats();
        this.setupAutoRefresh();
    },

    /**
     * Load dashboard statistics
     */
    loadStats: async function() {
        try {
            const stats = await API.getDashboardStats();
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
        }
    },

    /**
     * Update statistics display
     */
    updateStatsDisplay: function(stats) {
        // Update overview stats
        if (stats.overview) {
            this.updateElement('total-users', stats.overview.total_users);
            this.updateElement('total-devices', stats.overview.total_devices);
            this.updateElement('online-devices', stats.overview.online_devices);
            this.updateElement('open-tickets', stats.overview.open_tickets);
        }
    },

    /**
     * Update element content
     */
    updateElement: function(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    },

    /**
     * Setup auto-refresh
     */
    setupAutoRefresh: function() {
        setInterval(() => {
            this.loadStats();
            DeviceManager.loadDevices();
            NotificationManager.loadNotifications();
        }, IQHOME.REFRESH_INTERVAL);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard if on dashboard page
    if (document.querySelector('.dashboard-card')) {
        Dashboard.init();
    }
    
    // Load devices if device grid exists
    if (document.getElementById('devices-grid')) {
        DeviceManager.loadDevices();
    }
    
    // Load notifications if notification list exists
    if (document.getElementById('notifications-list')) {
        NotificationManager.loadNotifications();
    }
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Export for global access
window.IQHOME = IQHOME;
window.Utils = Utils;
window.API = API;
window.DeviceManager = DeviceManager;
window.NotificationManager = NotificationManager;
window.Dashboard = Dashboard;
