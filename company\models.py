from django.db import models
from django.utils import timezone
import uuid
from django.core.cache import cache
from django.conf import settings


class Service(models.Model):
    """
    نموذج الخدمات التي تقدمها الشركة
    """

    SERVICE_CATEGORIES = [
        ('installation', 'تركيب المنازل الذكية'),
        ('maintenance', 'صيانة وإصلاح'),
        ('consultation', 'استشارات تقنية'),
        ('upgrade', 'ترقية الأنظمة'),
        ('monitoring', 'خدمات المراقبة'),
        ('training', 'التدريب والتأهيل'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name_ar = models.CharField(max_length=200, verbose_name="اسم الخدمة بالعربية")
    name_en = models.CharField(max_length=200, verbose_name="اسم الخدمة بالإنجليزية")
    description_ar = models.TextField(verbose_name="وصف الخدمة بالعربية")
    description_en = models.TextField(verbose_name="وصف الخدمة بالإنجليزية")
    category = models.CharField(max_length=20, choices=SERVICE_CATEGORIES, verbose_name="فئة الخدمة")

    # تفاصيل الخدمة
    price_range_min = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="أقل سعر")
    price_range_max = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="أعلى سعر")
    duration_hours = models.IntegerField(verbose_name="مدة التنفيذ بالساعات")

    # الصور والملفات
    main_image = models.ImageField(upload_to='services/', verbose_name="الصورة الرئيسية")
    gallery_images = models.JSONField(default=list, blank=True, verbose_name="معرض الصور")

    # الميزات والمواصفات
    features = models.JSONField(default=list, verbose_name="الميزات")
    requirements = models.JSONField(default=list, verbose_name="المتطلبات")

    # حالة الخدمة
    is_active = models.BooleanField(default=True, verbose_name="نشطة")
    is_featured = models.BooleanField(default=False, verbose_name="خدمة مميزة")

    # التواريخ
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'services'
        verbose_name = 'خدمة'
        verbose_name_plural = 'الخدمات'
        ordering = ['-created_at']

    def __str__(self):
        return self.name_ar

    @property
    def price_range_display(self):
        """عرض نطاق السعر"""
        return f"{self.price_range_min:,.0f} - {self.price_range_max:,.0f} د.ع"


class ServiceRequest(models.Model):
    """
    نموذج طلبات الخدمات من العملاء
    """

    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('reviewing', 'قيد المراجعة'),
        ('approved', 'موافق عليه'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]

    # معرف الطلب
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    request_number = models.CharField(max_length=20, unique=True, editable=False, verbose_name="رقم الطلب")

    # الخدمة المطلوبة
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='requests', verbose_name="الخدمة")

    # بيانات العميل
    customer_name = models.CharField(max_length=100, verbose_name="اسم العميل")
    customer_phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    customer_email = models.EmailField(verbose_name="البريد الإلكتروني")
    customer_address = models.TextField(verbose_name="العنوان")
    customer_city = models.CharField(max_length=50, verbose_name="المدينة")

    # تفاصيل الطلب
    description = models.TextField(verbose_name="وصف الطلب")
    preferred_date = models.DateField(verbose_name="التاريخ المفضل")
    preferred_time = models.TimeField(verbose_name="الوقت المفضل")

    # حالة الطلب
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="حالة الطلب")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name="الأولوية")

    # التكلفة المقدرة
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="التكلفة المقدرة")
    final_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="التكلفة النهائية")

    # الملاحظات
    customer_notes = models.TextField(blank=True, verbose_name="ملاحظات العميل")
    admin_notes = models.TextField(blank=True, verbose_name="ملاحظات الإدارة")

    # التواريخ
    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإنجاز")

    # الموظف المسؤول
    assigned_to = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_requests',
        verbose_name="المسؤول"
    )

    class Meta:
        db_table = 'service_requests'
        verbose_name = 'طلب خدمة'
        verbose_name_plural = 'طلبات الخدمات'
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.request_number:
            # إنشاء رقم طلب: IQ-YYYYMMDD-XXXX
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
            last_request = ServiceRequest.objects.filter(
                request_number__startswith=f'IQ-{date_str}'
            ).order_by('-request_number').first()

            if last_request:
                last_num = int(last_request.request_number.split('-')[-1])
                new_num = last_num + 1
            else:
                new_num = 1

            self.request_number = f'IQ-{date_str}-{new_num:04d}'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.request_number} - {self.customer_name}"

    @property
    def is_pending(self):
        """التحقق من كون الطلب في الانتظار"""
        return self.status == 'pending'

    @property
    def is_completed(self):
        """التحقق من كون الطلب مكتمل"""
        return self.status == 'completed'


class ContactMessage(models.Model):
    """
    رسائل التواصل من الموقع
    """

    MESSAGE_TYPES = [
        ('inquiry', 'استفسار عام'),
        ('quote', 'طلب عرض سعر'),
        ('complaint', 'شكوى'),
        ('suggestion', 'اقتراح'),
        ('partnership', 'شراكة'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, verbose_name="الاسم")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    subject = models.CharField(max_length=200, verbose_name="الموضوع")
    message = models.TextField(verbose_name="الرسالة")
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='inquiry', verbose_name="نوع الرسالة")

    # حالة الرسالة
    is_read = models.BooleanField(default=False, verbose_name="مقروءة")
    is_replied = models.BooleanField(default=False, verbose_name="تم الرد")

    # الرد
    reply_message = models.TextField(blank=True, verbose_name="رسالة الرد")
    replied_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="تم الرد بواسطة"
    )
    replied_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الرد")

    created_at = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الإرسال")

    class Meta:
        db_table = 'contact_messages'
        verbose_name = 'رسالة تواصل'
        verbose_name_plural = 'رسائل التواصل'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"


class SiteSettings(models.Model):
    """
    إعدادات الموقع العامة
    """

    # معلومات الشركة الأساسية
    company_name_ar = models.CharField(max_length=100, default="IQHome", verbose_name="اسم الشركة (عربي)")
    company_name_en = models.CharField(max_length=100, default="IQHome", verbose_name="اسم الشركة (إنجليزي)")
    company_slogan_ar = models.CharField(max_length=200, default="الشركة الرائدة في تقنيات المنازل الذكية في العراق", verbose_name="شعار الشركة (عربي)")
    company_slogan_en = models.CharField(max_length=200, default="Leading Smart Home Technology Company in Iraq", verbose_name="شعار الشركة (إنجليزي)")

    # معلومات التواصل
    phone_primary = models.CharField(max_length=20, default="+*********** 4567", verbose_name="الهاتف الرئيسي")
    phone_secondary = models.CharField(max_length=20, blank=True, verbose_name="الهاتف الثانوي")
    email_primary = models.EmailField(default="<EMAIL>", verbose_name="البريد الإلكتروني الرئيسي")
    email_support = models.EmailField(default="<EMAIL>", verbose_name="بريد الدعم الفني")

    # معلومات العنوان
    address_ar = models.CharField(max_length=300, default="بغداد - الكرادة الداخل", verbose_name="العنوان (عربي)")
    address_en = models.CharField(max_length=300, default="Baghdad - Karrada", verbose_name="العنوان (إنجليزي)")
    address_details_ar = models.TextField(default="شارع الكرادة الداخل - مقابل مجمع بابل التجاري", verbose_name="تفاصيل العنوان (عربي)")
    address_details_en = models.TextField(default="Karrada Street - Opposite Babel Commercial Complex", verbose_name="تفاصيل العنوان (إنجليزي)")

    # ساعات العمل
    working_hours_ar = models.CharField(max_length=100, default="السبت - الخميس: 9:00 ص - 6:00 م", verbose_name="ساعات العمل (عربي)")
    working_hours_en = models.CharField(max_length=100, default="Saturday - Thursday: 9:00 AM - 6:00 PM", verbose_name="ساعات العمل (إنجليزي)")

    # وسائل التواصل الاجتماعي
    facebook_url = models.URLField(blank=True, verbose_name="رابط فيسبوك")
    twitter_url = models.URLField(blank=True, verbose_name="رابط تويتر")
    instagram_url = models.URLField(blank=True, verbose_name="رابط إنستغرام")
    linkedin_url = models.URLField(blank=True, verbose_name="رابط لينكد إن")
    whatsapp_number = models.CharField(max_length=20, blank=True, verbose_name="رقم واتساب")

    # إعدادات الموقع
    site_title_ar = models.CharField(max_length=100, default="IQHome - المنازل الذكية", verbose_name="عنوان الموقع (عربي)")
    site_title_en = models.CharField(max_length=100, default="IQHome - Smart Homes", verbose_name="عنوان الموقع (إنجليزي)")
    site_description_ar = models.TextField(default="نحول منزلك إلى منزل ذكي عصري وآمن بأحدث التقنيات العالمية", verbose_name="وصف الموقع (عربي)")
    site_description_en = models.TextField(default="Transform your home into a modern and secure smart home with the latest global technologies", verbose_name="وصف الموقع (إنجليزي)")

    # معلومات إضافية
    about_us_ar = models.TextField(default="IQHome هي الشركة الرائدة في مجال تقنيات المنازل الذكية في العراق، نتميز بخبرة واسعة وفريق متخصص في أحدث التقنيات العالمية.", verbose_name="من نحن (عربي)")
    about_us_en = models.TextField(default="IQHome is the leading company in smart home technologies in Iraq, distinguished by extensive experience and a specialized team in the latest global technologies.", verbose_name="من نحن (إنجليزي)")

    # إعدادات العرض
    show_stats = models.BooleanField(default=True, verbose_name="عرض الإحصائيات")
    stats_projects = models.IntegerField(default=500, verbose_name="عدد المشاريع")
    stats_experience = models.IntegerField(default=5, verbose_name="سنوات الخبرة")
    stats_customers = models.IntegerField(default=1000, verbose_name="عدد العملاء")

    # تواريخ التحديث
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'site_settings'
        verbose_name = 'إعدادات الموقع'
        verbose_name_plural = 'إعدادات الموقع'

    def save(self, *args, **kwargs):
        # مسح الكاش عند التحديث
        cache.delete('site_settings')
        super().save(*args, **kwargs)

    def __str__(self):
        return f"إعدادات الموقع - {self.company_name_ar}"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات الموقع مع التخزين المؤقت"""
        settings = cache.get('site_settings')
        if not settings:
            settings, created = cls.objects.get_or_create(pk=1)
            cache.set('site_settings', settings, 3600)  # تخزين لمدة ساعة
        return settings


class Portfolio(models.Model):
    """
    معرض الأعمال
    """

    MEDIA_TYPES = [
        ('image', 'صورة'),
        ('video', 'مقطع فيديو'),
        ('gallery', 'معرض صور'),
        ('360_view', 'عرض 360 درجة'),
    ]

    PROJECT_CATEGORIES = [
        ('smart_home', 'منزل ذكي كامل'),
        ('security', 'أنظمة الأمان'),
        ('lighting', 'أنظمة الإضاءة'),
        ('climate', 'التحكم في المناخ'),
        ('entertainment', 'أنظمة الترفيه'),
        ('automation', 'الأتمتة المنزلية'),
        ('other', 'أخرى'),
    ]

    # معلومات المشروع الأساسية
    title_ar = models.CharField(max_length=200, verbose_name="عنوان المشروع (عربي)")
    title_en = models.CharField(max_length=200, blank=True, verbose_name="عنوان المشروع (إنجليزي)")
    description_ar = models.TextField(verbose_name="وصف المشروع (عربي)")
    description_en = models.TextField(blank=True, verbose_name="وصف المشروع (إنجليزي)")

    # تصنيف المشروع
    category = models.CharField(max_length=20, choices=PROJECT_CATEGORIES, default='smart_home', verbose_name="فئة المشروع")
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="الخدمة المرتبطة")

    # معلومات المشروع
    client_name = models.CharField(max_length=100, blank=True, verbose_name="اسم العميل")
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")
    project_date = models.DateField(blank=True, null=True, verbose_name="تاريخ المشروع")
    duration = models.CharField(max_length=50, blank=True, verbose_name="مدة التنفيذ")

    # الوسائط
    media_type = models.CharField(max_length=20, choices=MEDIA_TYPES, default='image', verbose_name="نوع الوسائط")
    featured_image = models.ImageField(upload_to='portfolio/images/', blank=True, null=True, verbose_name="الصورة الرئيسية")
    video_url = models.URLField(blank=True, verbose_name="رابط الفيديو (YouTube/Vimeo)")
    video_file = models.FileField(upload_to='portfolio/videos/', blank=True, verbose_name="ملف الفيديو")

    # إعدادات العرض
    is_featured = models.BooleanField(default=False, verbose_name="مشروع مميز")
    is_published = models.BooleanField(default=True, verbose_name="منشور")
    show_on_homepage = models.BooleanField(default=False, verbose_name="عرض في الصفحة الرئيسية")
    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")

    # معلومات إضافية
    technologies_used = models.TextField(blank=True, verbose_name="التقنيات المستخدمة")
    project_cost = models.CharField(max_length=100, blank=True, verbose_name="تكلفة المشروع")
    client_feedback = models.TextField(blank=True, verbose_name="تقييم العميل")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'portfolio'
        verbose_name = 'مشروع في المعرض'
        verbose_name_plural = 'معرض الأعمال'
        ordering = ['-display_order', '-created_at']

    def __str__(self):
        return self.title_ar

    @property
    def get_media_type_display_icon(self):
        """أيقونة نوع الوسائط"""
        icons = {
            'image': 'fas fa-image',
            'video': 'fas fa-video',
            'gallery': 'fas fa-images',
            '360_view': 'fas fa-globe',
        }
        return icons.get(self.media_type, 'fas fa-file')


class PortfolioImage(models.Model):
    """
    صور إضافية للمشروع
    """
    portfolio = models.ForeignKey(Portfolio, on_delete=models.CASCADE, related_name='images', verbose_name="المشروع")
    image = models.ImageField(upload_to='portfolio/gallery/', verbose_name="الصورة")
    caption_ar = models.CharField(max_length=200, blank=True, verbose_name="وصف الصورة (عربي)")
    caption_en = models.CharField(max_length=200, blank=True, verbose_name="وصف الصورة (إنجليزي)")
    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")

    class Meta:
        db_table = 'portfolio_images'
        verbose_name = 'صورة المشروع'
        verbose_name_plural = 'صور المشاريع'
        ordering = ['display_order']

    def __str__(self):
        return f"{self.portfolio.title_ar} - صورة {self.id}"


class Testimonial(models.Model):
    """
    آراء العملاء
    """
    client_name = models.CharField(max_length=100, verbose_name="اسم العميل")
    client_position = models.CharField(max_length=100, blank=True, verbose_name="المنصب/الوظيفة")
    client_company = models.CharField(max_length=100, blank=True, verbose_name="الشركة")
    client_image = models.ImageField(upload_to='testimonials/', blank=True, verbose_name="صورة العميل")

    testimonial_ar = models.TextField(verbose_name="الرأي (عربي)")
    testimonial_en = models.TextField(blank=True, verbose_name="الرأي (إنجليزي)")

    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)], default=5, verbose_name="التقييم (1-5)")

    portfolio = models.ForeignKey(Portfolio, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المشروع المرتبط")
    service = models.ForeignKey(Service, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="الخدمة المرتبطة")

    is_featured = models.BooleanField(default=False, verbose_name="رأي مميز")
    is_published = models.BooleanField(default=True, verbose_name="منشور")
    show_on_homepage = models.BooleanField(default=False, verbose_name="عرض في الصفحة الرئيسية")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'testimonials'
        verbose_name = 'رأي العميل'
        verbose_name_plural = 'آراء العملاء'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.client_name} - {self.rating} نجوم"

    @property
    def stars_range(self):
        """نطاق النجوم للعرض"""
        return range(self.rating)

    @property
    def empty_stars_range(self):
        """النجوم الفارغة"""
        return range(5 - self.rating)


class FAQ(models.Model):
    """
    الأسئلة الشائعة
    """
    question_ar = models.CharField(max_length=300, verbose_name="السؤال (عربي)")
    question_en = models.CharField(max_length=300, blank=True, verbose_name="السؤال (إنجليزي)")
    answer_ar = models.TextField(verbose_name="الإجابة (عربي)")
    answer_en = models.TextField(blank=True, verbose_name="الإجابة (إنجليزي)")

    category = models.CharField(max_length=50, blank=True, verbose_name="الفئة")
    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")
    is_published = models.BooleanField(default=True, verbose_name="منشور")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'faq'
        verbose_name = 'سؤال شائع'
        verbose_name_plural = 'الأسئلة الشائعة'
        ordering = ['display_order', '-created_at']

    def __str__(self):
        return self.question_ar


class BlogPost(models.Model):
    """
    المقالات والأخبار
    """
    title_ar = models.CharField(max_length=200, verbose_name="عنوان المقال (عربي)")
    title_en = models.CharField(max_length=200, blank=True, verbose_name="عنوان المقال (إنجليزي)")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="الرابط المختصر")

    excerpt_ar = models.TextField(max_length=300, verbose_name="مقتطف (عربي)")
    excerpt_en = models.TextField(max_length=300, blank=True, verbose_name="مقتطف (إنجليزي)")
    content_ar = models.TextField(verbose_name="المحتوى (عربي)")
    content_en = models.TextField(blank=True, verbose_name="المحتوى (إنجليزي)")

    featured_image = models.ImageField(upload_to='blog/', verbose_name="الصورة الرئيسية")

    category = models.CharField(max_length=50, blank=True, verbose_name="الفئة")
    tags = models.CharField(max_length=200, blank=True, verbose_name="الكلمات المفتاحية")

    is_featured = models.BooleanField(default=False, verbose_name="مقال مميز")
    is_published = models.BooleanField(default=True, verbose_name="منشور")

    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="الكاتب")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ النشر")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'blog_posts'
        verbose_name = 'مقال'
        verbose_name_plural = 'المقالات والأخبار'
        ordering = ['-created_at']

    def __str__(self):
        return self.title_ar


class Team(models.Model):
    """
    فريق العمل
    """
    name_ar = models.CharField(max_length=100, verbose_name="الاسم (عربي)")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="الاسم (إنجليزي)")
    position_ar = models.CharField(max_length=100, verbose_name="المنصب (عربي)")
    position_en = models.CharField(max_length=100, blank=True, verbose_name="المنصب (إنجليزي)")

    bio_ar = models.TextField(blank=True, verbose_name="نبذة شخصية (عربي)")
    bio_en = models.TextField(blank=True, verbose_name="نبذة شخصية (إنجليزي)")

    photo = models.ImageField(upload_to='team/', verbose_name="الصورة الشخصية")

    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")

    # وسائل التواصل الاجتماعي
    linkedin_url = models.URLField(blank=True, verbose_name="لينكد إن")
    twitter_url = models.URLField(blank=True, verbose_name="تويتر")

    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'team'
        verbose_name = 'عضو فريق'
        verbose_name_plural = 'فريق العمل'
        ordering = ['display_order', 'name_ar']

    def __str__(self):
        return f"{self.name_ar} - {self.position_ar}"


class Partner(models.Model):
    """
    الشركاء والعملاء
    """
    name_ar = models.CharField(max_length=100, verbose_name="اسم الشريك (عربي)")
    name_en = models.CharField(max_length=100, blank=True, verbose_name="اسم الشريك (إنجليزي)")
    logo = models.ImageField(upload_to='partners/', verbose_name="الشعار")
    website_url = models.URLField(blank=True, verbose_name="الموقع الإلكتروني")

    description_ar = models.TextField(blank=True, verbose_name="الوصف (عربي)")
    description_en = models.TextField(blank=True, verbose_name="الوصف (إنجليزي)")

    partner_type = models.CharField(max_length=20, choices=[
        ('client', 'عميل'),
        ('supplier', 'مورد'),
        ('partner', 'شريك'),
        ('sponsor', 'راعي'),
    ], default='client', verbose_name="نوع الشراكة")

    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    show_on_homepage = models.BooleanField(default=False, verbose_name="عرض في الصفحة الرئيسية")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        db_table = 'partners'
        verbose_name = 'شريك'
        verbose_name_plural = 'الشركاء والعملاء'
        ordering = ['display_order', 'name_ar']

    def __str__(self):
        return self.name_ar
