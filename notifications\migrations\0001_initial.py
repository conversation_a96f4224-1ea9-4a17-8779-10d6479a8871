# Generated by Django 5.2.4 on 2025-07-29 02:14

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('devices', '0001_initial'),
        ('support', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('warning', 'Warning'), ('error', 'Error'), ('success', 'Success'), ('device_alert', 'Device Alert'), ('system', 'System Notification'), ('support', 'Support Update')], default='info', max_length=20)),
                ('status', models.CharField(choices=[('unread', 'Unread'), ('read', 'Read'), ('archived', 'Archived')], default='unread', max_length=20)),
                ('is_broadcast', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('extra_data', models.JSONField(blank=True, null=True)),
                ('related_device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='devices.device')),
                ('related_ticket', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='support.supportticket')),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['target_user', 'status', '-created_at'], name='notificatio_target__d3284d_idx'), models.Index(fields=['is_broadcast', '-created_at'], name='notificatio_is_broa_b9424d_idx')],
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('user_login', 'User Login'), ('user_logout', 'User Logout'), ('user_created', 'User Created'), ('user_updated', 'User Updated'), ('user_deleted', 'User Deleted'), ('device_added', 'Device Added'), ('device_updated', 'Device Updated'), ('device_removed', 'Device Removed'), ('device_command', 'Device Command'), ('ticket_created', 'Support Ticket Created'), ('ticket_updated', 'Support Ticket Updated'), ('system_error', 'System Error'), ('admin_action', 'Admin Action'), ('security_event', 'Security Event')], max_length=30)),
                ('level', models.CharField(choices=[('debug', 'Debug'), ('info', 'Info'), ('warning', 'Warning'), ('error', 'Error'), ('critical', 'Critical')], default='info', max_length=10)),
                ('description', models.TextField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('extra_data', models.JSONField(blank=True, null=True)),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Log',
                'verbose_name_plural': 'System Logs',
                'db_table': 'system_logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['action', '-timestamp'], name='system_logs_action_905428_idx'), models.Index(fields=['performed_by', '-timestamp'], name='system_logs_perform_fedcdc_idx'), models.Index(fields=['level', '-timestamp'], name='system_logs_level_837293_idx')],
            },
        ),
    ]
