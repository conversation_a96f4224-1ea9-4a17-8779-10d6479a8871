<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة معرض الأعمال - {{ company_name }}</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Professional CSS -->
    <link href="/static/css/dark-professional.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل الاحترافي -->
    <nav class="navbar navbar-expand-lg navbar-dark-pro fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand-dark" href="{% url 'company:admin_dashboard' %}">
                <i class="fas fa-shield-alt me-2"></i>{{ company_name }} - لوحة التحكم
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn-secondary-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>{{ user.get_full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-dark">
                        <li><a class="dropdown-item text-secondary-dark" href="{% url 'company:admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content-fixed">
        <div class="row">
            <!-- الشريط الجانبي الاحترافي -->
            <div class="col-md-3 col-lg-2 sidebar-dark-pro">
                <ul class="sidebar-menu-dark">
                    <li>
                        <a href="{% url 'company:admin_dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_services' %}">
                            <i class="fas fa-cogs"></i>
                            إدارة الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_requests' %}">
                            <i class="fas fa-clipboard-list"></i>
                            طلبات الخدمات
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_messages' %}">
                            <i class="fas fa-envelope"></i>
                            رسائل التواصل
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_portfolio' %}" class="active">
                            <i class="fas fa-images"></i>
                            معرض الأعمال
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_testimonials' %}">
                            <i class="fas fa-star"></i>
                            آراء العملاء
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_content' %}">
                            <i class="fas fa-file-alt"></i>
                            إدارة المحتوى
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'company:admin_settings' %}">
                            <i class="fas fa-cog"></i>
                            إعدادات الموقع
                        </a>
                    </li>
                    <li>
                        <a href="/">
                            <i class="fas fa-globe"></i>
                            الموقع الرئيسي
                        </a>
                    </li>
                </ul>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- رأس الصفحة -->
                <div class="card-dark-pro">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="card-title-dark">
                                <i class="fas fa-images me-2"></i>إدارة معرض الأعمال
                            </h1>
                            <p class="text-secondary-dark">إدارة وتنظيم مشاريع الشركة ومعرض الأعمال</p>
                        </div>
                        <div>
                            <a href="{% url 'company:admin_portfolio_add' %}" class="btn-dark-pro">
                                <i class="fas fa-plus"></i>إضافة مشروع جديد
                            </a>
                        </div>
                    </div>
                </div>

                <!-- رسائل النجاح والخطأ -->
                {% if messages %}
                    {% for message in messages %}
                        {% if 'admin' in request.path %}
                        <div class="alert alert-{{ message.tags|cut:'admin_portfolio'|cut:'admin_' }} alert-dismissible fade show" role="alert">
                            {% if 'success' in message.tags %}
                                <i class="fas fa-check-circle me-2"></i>
                            {% elif 'warning' in message.tags %}
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            {% elif 'error' in message.tags or 'danger' in message.tags %}
                                <i class="fas fa-times-circle me-2"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}

                <!-- إحصائيات المعرض -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ total_projects }}</div>
                            <div class="stat-label-dark">إجمالي المشاريع</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-folder"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ published_projects }}</div>
                            <div class="stat-label-dark">مشاريع منشورة</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ featured_projects }}</div>
                            <div class="stat-label-dark">مشاريع مميزة</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card-dark">
                            <div class="stat-number-dark">{{ draft_projects }}</div>
                            <div class="stat-label-dark">مسودات</div>
                            <div class="stat-icon-dark">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات أنواع الوسائط -->
                <div class="row g-4 mb-4">
                    <div class="col-md-4">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-chart-pie me-2"></i>أنواع الوسائط
                                </h5>
                            </div>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-mini-dark">
                                        <div class="stat-number-mini">{{ image_projects }}</div>
                                        <div class="stat-label-mini">صور</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini-dark">
                                        <div class="stat-number-mini">{{ video_projects }}</div>
                                        <div class="stat-label-mini">فيديو</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini-dark">
                                        <div class="stat-number-mini">{{ gallery_projects }}</div>
                                        <div class="stat-label-mini">معارض</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-chart-bar me-2"></i>إحصائيات الفئات
                                </h5>
                            </div>
                            <div class="row">
                                {% for category, count in category_stats.items %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-secondary-dark">{{ category }}</span>
                                        <span class="badge-primary">{{ count }}</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المشاريع -->
                <div class="card-dark-pro">
                    <div class="card-header-dark">
                        <h3 class="card-title-dark">
                            <i class="fas fa-list me-2"></i>قائمة المشاريع
                        </h3>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table-dark-pro">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>عنوان المشروع</th>
                                    <th>الفئة</th>
                                    <th>نوع الوسائط</th>
                                    <th>العميل</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects %}
                                <tr>
                                    <td>
                                        {% if project.featured_image %}
                                            <img src="{{ project.featured_image.url }}" alt="{{ project.title_ar }}"
                                                 style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                                        {% else %}
                                            <div class="d-flex align-items-center justify-content-center"
                                                 style="width: 60px; height: 40px; background: var(--charcoal-medium); border-radius: 4px;">
                                                <i class="fas fa-image text-muted-dark"></i>
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>
                                            <strong class="text-primary-dark">{{ project.title_ar }}</strong>
                                            {% if project.is_featured %}
                                                <span class="badge-warning ms-2">
                                                    <i class="fas fa-star"></i>مميز
                                                </span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted-dark">{{ project.description_ar|truncatewords:8 }}</small>
                                    </td>
                                    <td>
                                        <span class="badge-secondary">{{ project.get_category_display }}</span>
                                    </td>
                                    <td>
                                        <span class="badge-info">
                                            <i class="{{ project.get_media_type_display_icon }}"></i>
                                            {{ project.get_media_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if project.client_name %}
                                            <span class="text-secondary-dark">{{ project.client_name }}</span>
                                        {% else %}
                                            <span class="text-muted-dark">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if project.is_published %}
                                            <span class="badge-success">منشور</span>
                                        {% else %}
                                            <span class="badge-warning">مسودة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted-dark">{{ project.created_at|date:"d/m/Y" }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'company:admin_portfolio_edit' project.id %}" class="btn-outline-dark btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'company:portfolio_detail' project.id %}" target="_blank" class="btn-outline-dark btn-sm" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form method="post" action="{% url 'company:admin_portfolio_toggle' project.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn-outline-{% if project.is_published %}warning{% else %}success{% endif %} btn-sm" title="{% if project.is_published %}إلغاء النشر{% else %}نشر{% endif %}">
                                                    <i class="fas fa-{% if project.is_published %}eye-slash{% else %}eye{% endif %}"></i>
                                                </button>
                                            </form>
                                            <button class="btn-outline-danger btn-sm" onclick="deleteProject('{{ project.id }}')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-folder-open fa-2x text-muted-dark mb-2"></i>
                                        <p class="text-muted-dark">لا توجد مشاريع في المعرض</p>
                                        <a href="/admin/company/portfolio/add/" class="btn-dark-pro">
                                            <i class="fas fa-plus"></i>إضافة أول مشروع
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="row g-4 mt-4">
                    <div class="col-md-6">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-link me-2"></i>روابط سريعة
                                </h5>
                            </div>
                            <div class="d-grid gap-2">
                                <a href="/admin/company/portfolio/" class="btn-outline-dark">
                                    <i class="fas fa-cog"></i>إدارة المشاريع (Django Admin)
                                </a>
                                <a href="{% url 'company:portfolio' %}" target="_blank" class="btn-outline-dark">
                                    <i class="fas fa-eye"></i>عرض المعرض في الموقع
                                </a>
                                <a href="/admin/company/portfolioimage/" class="btn-outline-dark">
                                    <i class="fas fa-images"></i>إدارة صور المشاريع
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card-dark-pro">
                            <div class="card-header-dark">
                                <h5 class="card-title-dark">
                                    <i class="fas fa-info-circle me-2"></i>نصائح
                                </h5>
                            </div>
                            <ul class="list-unstyled mb-0">
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    استخدم صور عالية الجودة للمشاريع
                                </li>
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    اكتب وصف واضح ومفصل لكل مشروع
                                </li>
                                <li class="text-secondary-dark mb-2">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    حدد المشاريع المميزة لعرضها أولاً
                                </li>
                                <li class="text-secondary-dark">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>
                                    أضف معلومات العميل والموقع إن أمكن
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Dark Professional JS -->
    <script src="/static/js/dark-professional.js"></script>
    
    <script>
        function deleteProject(projectId) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                // إنشاء نموذج مخفي للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/portfolio/delete/${projectId}/`;

                // إضافة CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken.value;
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        }

        // إضافة CSRF token للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = '{{ csrf_token }}';
                document.body.appendChild(csrfInput);
            }
        });
    </script>
</body>
</html>
