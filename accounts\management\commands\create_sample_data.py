from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import User
from devices.models import <PERSON><PERSON>, DeviceData, ControlCommand
from support.models import SupportTicket, TicketMessage
from notifications.models import Notification, SystemLog
import random
from datetime import timedelta


class Command(BaseCommand):
    help = 'Create sample data for IQHome platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=10,
            help='Number of users to create',
        )
        parser.add_argument(
            '--devices',
            type=int,
            default=20,
            help='Number of devices to create',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data for IQHome...')
        
        # Create users
        users_count = options['users']
        devices_count = options['devices']
        
        self.create_users(users_count)
        self.create_devices(devices_count)
        self.create_support_tickets()
        self.create_notifications()
        self.create_system_logs()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data: {users_count} users, {devices_count} devices'
            )
        )

    def create_users(self, count):
        """Create sample users"""
        self.stdout.write(f'Creating {count} users...')
        
        for i in range(count):
            username = f'user{i+1}'
            email = f'user{i+1}@iqhome.com'
            
            if not User.objects.filter(username=username).exists():
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password='password123',
                    first_name=f'User{i+1}',
                    last_name='Test',
                    phone=f'+964770{1000000 + i}',
                    role='user'
                )
                self.stdout.write(f'Created user: {username}')

    def create_devices(self, count):
        """Create sample devices"""
        self.stdout.write(f'Creating {count} devices...')
        
        device_types = ['light', 'switch', 'sensor', 'camera', 'thermostat', 'lock', 'outlet']
        locations = ['Living Room', 'Kitchen', 'Bedroom', 'Bathroom', 'Garden', 'Garage']
        statuses = ['online', 'offline', 'error']
        
        users = User.objects.filter(role='user')
        
        for i in range(count):
            user = random.choice(users)
            device_type = random.choice(device_types)
            location = random.choice(locations)
            status = random.choice(statuses)
            
            # Generate MAC address
            mac = ':'.join([f'{random.randint(0, 255):02x}' for _ in range(6)])
            
            device = Device.objects.create(
                name=f'{location} {device_type.title()} {i+1}',
                device_type=device_type,
                mac_address=mac,
                ip_address=f'192.168.1.{100 + i}',
                status=status,
                user=user,
                location=location,
                manufacturer='IQHome',
                model_number=f'IQ-{device_type.upper()}-{i+1:03d}',
                firmware_version='1.0.0'
            )
            
            # Create device data
            self.create_device_data(device)
            
            # Create control commands
            self.create_control_commands(device)
            
            self.stdout.write(f'Created device: {device.name}')

    def create_device_data(self, device):
        """Create sample device data"""
        data_types = {
            'light': [('brightness', '%'), ('power_consumption', 'W')],
            'sensor': [('temperature', '°C'), ('humidity', '%')],
            'thermostat': [('temperature', '°C'), ('target_temp', '°C')],
            'energy': [('power_consumption', 'kWh'), ('voltage', 'V')],
        }
        
        device_data_types = data_types.get(device.device_type, [('status', '')])
        
        # Create data for the last 7 days
        for days_ago in range(7):
            timestamp = timezone.now() - timedelta(days=days_ago)
            
            for key, unit in device_data_types:
                if key == 'temperature':
                    value = random.randint(18, 30)
                elif key == 'humidity':
                    value = random.randint(30, 80)
                elif key == 'brightness':
                    value = random.randint(0, 100)
                elif key == 'power_consumption':
                    value = round(random.uniform(0.1, 5.0), 2)
                else:
                    value = random.randint(0, 100)
                
                DeviceData.objects.create(
                    device=device,
                    timestamp=timestamp,
                    key=key,
                    value=str(value),
                    unit=unit
                )

    def create_control_commands(self, device):
        """Create sample control commands"""
        commands = ['turn_on', 'turn_off', 'set_brightness', 'reset']
        statuses = ['completed', 'failed', 'pending']
        
        for _ in range(random.randint(1, 5)):
            command = random.choice(commands)
            status = random.choice(statuses)
            
            ControlCommand.objects.create(
                device=device,
                command_type=command,
                status=status,
                issued_by=device.user,
                timestamp=timezone.now() - timedelta(hours=random.randint(1, 48))
            )

    def create_support_tickets(self):
        """Create sample support tickets"""
        self.stdout.write('Creating support tickets...')
        
        categories = ['technical', 'device', 'account', 'billing', 'feature', 'bug']
        priorities = ['low', 'medium', 'high', 'urgent']
        statuses = ['open', 'in_progress', 'resolved', 'closed']
        
        users = User.objects.filter(role='user')
        admin_users = User.objects.filter(role='admin')
        
        for i in range(15):
            user = random.choice(users)
            category = random.choice(categories)
            priority = random.choice(priorities)
            status = random.choice(statuses)
            
            ticket = SupportTicket.objects.create(
                title=f'Sample ticket {i+1} - {category} issue',
                description=f'This is a sample {category} support ticket for testing purposes.',
                category=category,
                priority=priority,
                status=status,
                user=user,
                assigned_to=random.choice(admin_users) if admin_users and random.choice([True, False]) else None
            )
            
            # Create ticket messages
            for j in range(random.randint(1, 3)):
                TicketMessage.objects.create(
                    ticket=ticket,
                    sender=random.choice([user] + list(admin_users)),
                    message=f'Sample message {j+1} for ticket {ticket.ticket_number}',
                    is_internal=random.choice([True, False]) if admin_users else False
                )

    def create_notifications(self):
        """Create sample notifications"""
        self.stdout.write('Creating notifications...')
        
        notification_types = ['info', 'warning', 'error', 'success', 'device_alert', 'system']
        users = User.objects.filter(role='user')
        
        for i in range(20):
            notification_type = random.choice(notification_types)
            target_user = random.choice(users) if random.choice([True, False]) else None
            is_broadcast = target_user is None
            
            Notification.objects.create(
                title=f'Sample {notification_type} notification {i+1}',
                message=f'This is a sample {notification_type} notification for testing.',
                notification_type=notification_type,
                target_user=target_user,
                is_broadcast=is_broadcast,
                status=random.choice(['unread', 'read'])
            )

    def create_system_logs(self):
        """Create sample system logs"""
        self.stdout.write('Creating system logs...')
        
        actions = ['user_login', 'user_logout', 'device_added', 'device_command', 'ticket_created']
        levels = ['info', 'warning', 'error']
        users = User.objects.all()
        
        for i in range(50):
            action = random.choice(actions)
            level = random.choice(levels)
            user = random.choice(users) if random.choice([True, False]) else None
            
            SystemLog.objects.create(
                action=action,
                level=level,
                description=f'Sample {action} log entry {i+1}',
                performed_by=user,
                timestamp=timezone.now() - timedelta(hours=random.randint(1, 168)),  # Last week
                ip_address=f'192.168.1.{random.randint(1, 254)}'
            )
