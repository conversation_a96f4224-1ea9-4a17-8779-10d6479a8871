from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from .models import Device, DeviceData, ControlCommand
import uuid

User = get_user_model()


class DeviceModelTest(TestCase):
    """Test cases for Device model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.device_data = {
            'name': 'Test Light',
            'device_type': 'light',
            'mac_address': '00:11:22:33:44:55',
            'ip_address': '*************',
            'user': self.user,
            'location': 'Living Room'
        }

    def test_create_device(self):
        """Test creating a device"""
        device = Device.objects.create(**self.device_data)

        self.assertEqual(device.name, 'Test Light')
        self.assertEqual(device.device_type, 'light')
        self.assertEqual(device.user, self.user)
        self.assertEqual(device.status, 'offline')  # Default status
        self.assertTrue(device.is_active)  # Default active
        self.assertIsInstance(device.id, uuid.UUID)

    def test_device_str_method(self):
        """Test device string representation"""
        device = Device.objects.create(**self.device_data)
        expected_str = f"{device.name} ({device.get_device_type_display()}) - {device.user.username}"
        self.assertEqual(str(device), expected_str)

    def test_device_is_online_property(self):
        """Test is_online property"""
        device = Device.objects.create(**self.device_data)

        # Test offline status
        device.status = 'offline'
        self.assertFalse(device.is_online)

        # Test online status
        device.status = 'online'
        self.assertTrue(device.is_online)

    def test_update_last_seen(self):
        """Test update_last_seen method"""
        device = Device.objects.create(**self.device_data)
        original_last_seen = device.last_seen

        device.update_last_seen()
        device.refresh_from_db()

        self.assertNotEqual(device.last_seen, original_last_seen)
        self.assertIsNotNone(device.last_seen)


class DeviceDataModelTest(TestCase):
    """Test cases for DeviceData model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.device = Device.objects.create(
            name='Test Sensor',
            device_type='sensor',
            mac_address='00:11:22:33:44:55',
            user=self.user
        )

    def test_create_device_data(self):
        """Test creating device data"""
        data = DeviceData.objects.create(
            device=self.device,
            key='temperature',
            value='25.5',
            unit='°C'
        )

        self.assertEqual(data.device, self.device)
        self.assertEqual(data.key, 'temperature')
        self.assertEqual(data.value, '25.5')
        self.assertEqual(data.unit, '°C')
        self.assertIsNotNone(data.timestamp)

    def test_device_data_str_method(self):
        """Test device data string representation"""
        data = DeviceData.objects.create(
            device=self.device,
            key='temperature',
            value='25.5',
            unit='°C'
        )
        expected_str = f"{self.device.name} - temperature: 25.5 °C"
        self.assertEqual(str(data), expected_str)


class ControlCommandModelTest(TestCase):
    """Test cases for ControlCommand model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.device = Device.objects.create(
            name='Test Light',
            device_type='light',
            mac_address='00:11:22:33:44:55',
            user=self.user
        )

    def test_create_control_command(self):
        """Test creating a control command"""
        command = ControlCommand.objects.create(
            device=self.device,
            command_type='turn_on',
            issued_by=self.user
        )

        self.assertEqual(command.device, self.device)
        self.assertEqual(command.command_type, 'turn_on')
        self.assertEqual(command.issued_by, self.user)
        self.assertEqual(command.status, 'pending')  # Default status
        self.assertIsNotNone(command.timestamp)

    def test_mark_as_sent(self):
        """Test mark_as_sent method"""
        command = ControlCommand.objects.create(
            device=self.device,
            command_type='turn_on',
            issued_by=self.user
        )

        command.mark_as_sent()

        self.assertEqual(command.status, 'sent')
        self.assertIsNotNone(command.sent_at)

    def test_mark_as_completed(self):
        """Test mark_as_completed method"""
        command = ControlCommand.objects.create(
            device=self.device,
            command_type='turn_on',
            issued_by=self.user
        )

        response_data = {'result': 'success'}
        command.mark_as_completed(response_data)

        self.assertEqual(command.status, 'completed')
        self.assertIsNotNone(command.completed_at)
        self.assertEqual(command.response_data, response_data)

    def test_mark_as_failed(self):
        """Test mark_as_failed method"""
        command = ControlCommand.objects.create(
            device=self.device,
            command_type='turn_on',
            issued_by=self.user
        )

        error_message = 'Device not responding'
        command.mark_as_failed(error_message)

        self.assertEqual(command.status, 'failed')
        self.assertIsNotNone(command.completed_at)
        self.assertEqual(command.error_message, error_message)
