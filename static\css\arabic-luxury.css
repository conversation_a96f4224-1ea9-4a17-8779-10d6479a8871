/* IQHome - تصميم عربي فخم وأنيق */

@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
    /* الألوان الفخمة */
    --primary-gold: #D4AF37;
    --primary-dark-gold: #B8941F;
    --secondary-blue: #1B365D;
    --accent-teal: #2C5F5D;
    --luxury-black: #1A1A1A;
    --elegant-gray: #F8F9FA;
    --warm-white: #FEFEFE;
    --text-dark: #2C3E50;
    --text-light: #6C757D;
    
    /* تدرجات فخمة */
    --gradient-gold: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8941F 100%);
    --gradient-blue: linear-gradient(135deg, #1B365D 0%, #2C5F5D 100%);
    --gradient-dark: linear-gradient(135deg, #1A1A1A 0%, #2C3E50 100%);
    
    /* الظلال الأنيقة */
    --shadow-luxury: 0 10px 40px rgba(212, 175, 55, 0.2);
    --shadow-elegant: 0 5px 20px rgba(0, 0, 0, 0.1);
    --shadow-deep: 0 15px 50px rgba(0, 0, 0, 0.3);
    
    /* الخطوط */
    --font-arabic: 'Tajawal', 'Arial', sans-serif;
    --font-elegant: 'Amiri', 'Times New Roman', serif;
    
    /* المسافات */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --spacing-xl: 5rem;
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

html {
    direction: rtl;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-arabic);
    background-color: var(--warm-white);
    color: var(--text-dark);
    line-height: 1.8;
    font-weight: 400;
    overflow-x: hidden;
}

/* العناوين الفخمة */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-elegant);
    font-weight: 700;
    color: var(--luxury-black);
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: 3.5rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
    font-size: 2.8rem;
    color: var(--secondary-blue);
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 80px;
    height: 3px;
    background: var(--gradient-gold);
    border-radius: 2px;
}

h3 {
    font-size: 2.2rem;
    color: var(--accent-teal);
}

/* الأزرار الفخمة */
.btn-luxury {
    background: var(--gradient-gold);
    border: none;
    color: var(--luxury-black);
    font-family: var(--font-arabic);
    font-weight: 600;
    padding: 15px 40px;
    border-radius: 50px;
    transition: all 0.4s ease;
    box-shadow: var(--shadow-luxury);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-luxury::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.btn-luxury:hover::before {
    left: 100%;
}

.btn-luxury:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 50px rgba(212, 175, 55, 0.4);
    color: var(--luxury-black);
}

.btn-elegant {
    background: var(--gradient-blue);
    border: 2px solid var(--primary-gold);
    color: var(--warm-white);
    font-family: var(--font-arabic);
    font-weight: 500;
    padding: 12px 35px;
    border-radius: 30px;
    transition: all 0.3s ease;
}

.btn-elegant:hover {
    background: var(--primary-gold);
    color: var(--luxury-black);
    transform: translateY(-2px);
    box-shadow: var(--shadow-elegant);
}

/* البطاقات الفخمة */
.card-luxury {
    background: var(--warm-white);
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow-elegant);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
}

.card-luxury::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-gold);
}

.card-luxury:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-deep);
}

.card-luxury .card-body {
    padding: var(--spacing-md);
}

.card-luxury .card-title {
    color: var(--secondary-blue);
    font-family: var(--font-elegant);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

/* القسم البطولي */
.hero-section {
    background: var(--gradient-dark);
    color: var(--warm-white);
    padding: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(212,175,55,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-family: var(--font-elegant);
    margin-bottom: var(--spacing-md);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-lg);
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* أقسام الخدمات */
.services-section {
    padding: var(--spacing-xl) 0;
    background: var(--elegant-gray);
}

.service-card {
    background: var(--warm-white);
    border-radius: 15px;
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(212, 175, 55, 0.2);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-luxury);
    border-color: var(--primary-gold);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-sm);
    font-size: 2rem;
    color: var(--luxury-black);
}

/* النماذج الأنيقة */
.form-luxury {
    background: var(--warm-white);
    padding: var(--spacing-lg);
    border-radius: 20px;
    box-shadow: var(--shadow-elegant);
}

.form-control-luxury {
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 10px;
    padding: 15px 20px;
    font-family: var(--font-arabic);
    transition: all 0.3s ease;
    background: var(--warm-white);
}

.form-control-luxury:focus {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
    outline: none;
}

.form-label-luxury {
    color: var(--secondary-blue);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-arabic);
}

/* التذييل الفخم */
.footer-luxury {
    background: var(--gradient-dark);
    color: var(--warm-white);
    padding: var(--spacing-xl) 0 var(--spacing-md);
    position: relative;
}

.footer-luxury::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-gold);
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* تأثيرات التمرير */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* شريط التنقل الفخم */
.navbar-luxury {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--primary-gold);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-luxury.scrolled {
    background: var(--luxury-black);
    box-shadow: var(--shadow-elegant);
}

.navbar-brand-luxury {
    font-family: var(--font-elegant);
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold) !important;
    text-decoration: none;
}

.nav-link-luxury {
    color: var(--warm-white) !important;
    font-weight: 500;
    padding: 0.5rem 1.5rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link-luxury::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: all 0.3s ease;
}

.nav-link-luxury:hover::after,
.nav-link-luxury.active::after {
    width: 100%;
    right: 0;
}

.nav-link-luxury:hover {
    color: var(--primary-gold) !important;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .hero-title {
        font-size: 2.8rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .btn-luxury {
        padding: 12px 30px;
        font-size: 0.9rem;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* تأثيرات خاصة */
.text-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: var(--gradient-gold) 1;
}

.bg-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(44, 95, 93, 0.1) 0%, transparent 50%);
}
