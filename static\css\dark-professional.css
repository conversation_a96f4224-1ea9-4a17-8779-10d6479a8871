/* IQHome - تصميم احترافي أنيق داكن فحم موحد */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    /* الألوان الداكنة الاحترافية */
    --charcoal-dark: #1a1a1a;
    --charcoal-medium: #2d2d2d;
    --charcoal-light: #404040;
    --charcoal-lighter: #525252;
    --accent-blue: #3b82f6;
    --accent-blue-hover: #2563eb;
    --accent-green: #10b981;
    --accent-orange: #f59e0b;
    --accent-red: #ef4444;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --border-light: #4b5563;
    
    /* التدرجات الاحترافية */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    --gradient-card: linear-gradient(145deg, #2d2d2d 0%, #1a1a1a 100%);
    
    /* الظلال الاحترافية */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.6);
    
    /* الخطوط */
    --font-arabic: 'Cairo', sans-serif;
    --font-english: 'Inter', sans-serif;
    
    /* المسافات */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* الحدود */
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    direction: rtl;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-arabic);
    background-color: var(--charcoal-dark);
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 400;
    overflow-x: hidden;
}

/* العناوين الاحترافية */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-arabic);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* الروابط */
a {
    color: var(--accent-blue);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--accent-blue-hover);
}

/* شريط التنقل الاحترافي */
.navbar-dark-pro {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
    transition: all 0.3s ease;
}

.navbar-dark-pro.scrolled {
    background: var(--charcoal-dark);
    box-shadow: var(--shadow-md);
}

.navbar-brand-dark {
    font-family: var(--font-arabic);
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--text-primary) !important;
    text-decoration: none;
}

.nav-link-dark {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-md) !important;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link-dark::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-blue);
    transition: all 0.3s ease;
    transform: translateX(50%);
}

.nav-link-dark:hover::after,
.nav-link-dark.active::after {
    width: 80%;
}

.nav-link-dark:hover,
.nav-link-dark.active {
    color: var(--text-primary) !important;
}

/* الأزرار الاحترافية */
.btn-dark-pro {
    background: var(--gradient-primary);
    border: none;
    color: var(--text-primary);
    font-family: var(--font-arabic);
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    cursor: pointer;
}

.btn-dark-pro:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-primary);
}

.btn-outline-dark {
    background: transparent;
    border: 2px solid var(--accent-blue);
    color: var(--accent-blue);
    font-family: var(--font-arabic);
    font-weight: 600;
    padding: calc(var(--spacing-sm) - 2px) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
}

.btn-outline-dark:hover {
    background: var(--accent-blue);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary-dark {
    background: var(--charcoal-medium);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-family: var(--font-arabic);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.btn-secondary-dark:hover {
    background: var(--charcoal-light);
    color: var(--text-primary);
    border-color: var(--border-light);
}

/* البطاقات الاحترافية */
.card-dark-pro {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-dark-pro::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-dark-pro:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-light);
}

.card-dark-pro:hover::before {
    opacity: 1;
}

.card-header-dark {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.card-title-dark {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* الجداول الاحترافية */
.table-dark-pro {
    width: 100%;
    border-collapse: collapse;
    background: var(--charcoal-medium);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table-dark-pro thead {
    background: var(--charcoal-dark);
}

.table-dark-pro th {
    padding: var(--spacing-md);
    text-align: right;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-dark-pro td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.table-dark-pro tbody tr {
    transition: all 0.2s ease;
}

.table-dark-pro tbody tr:hover {
    background: rgba(59, 130, 246, 0.1);
}

/* النماذج الاحترافية */
.form-dark-pro {
    background: var(--charcoal-medium);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.form-group-dark {
    margin-bottom: var(--spacing-md);
}

.form-label-dark {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-control-dark {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--charcoal-dark);
    color: var(--text-primary);
    font-family: var(--font-arabic);
    transition: all 0.3s ease;
}

.form-control-dark:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--charcoal-medium);
}

.form-control-dark::placeholder {
    color: var(--text-muted);
}

/* الإحصائيات الاحترافية */
.stat-card-dark {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.stat-card-dark:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-number-dark {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-blue);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-label-dark {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.stat-icon-dark {
    font-size: 2rem;
    color: var(--accent-blue);
    margin-bottom: var(--spacing-sm);
    opacity: 0.8;
}

/* الشارات الاحترافية */
.badge-dark {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary { background: var(--accent-blue); color: var(--text-primary); }
.badge-success { background: var(--accent-green); color: var(--text-primary); }
.badge-warning { background: var(--accent-orange); color: var(--charcoal-dark); }
.badge-danger { background: var(--accent-red); color: var(--text-primary); }
.badge-secondary { background: var(--charcoal-light); color: var(--text-secondary); }

/* التنبيهات الاحترافية */
.alert-dark {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--accent-green);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    color: var(--accent-orange);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: var(--accent-red);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    color: var(--accent-blue);
}

/* الشريط الجانبي الاحترافي */
.sidebar-dark-pro {
    background: var(--charcoal-medium);
    border-left: 1px solid var(--border-color);
    min-height: 100vh;
    padding: var(--spacing-lg) 0;
}

.sidebar-menu-dark {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu-dark li {
    margin: var(--spacing-xs) 0;
}

.sidebar-menu-dark a {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    margin: 0 var(--spacing-sm);
    position: relative;
}

.sidebar-menu-dark a::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--accent-blue);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-menu-dark a:hover,
.sidebar-menu-dark a.active {
    background: rgba(59, 130, 246, 0.1);
    color: var(--text-primary);
}

.sidebar-menu-dark a.active::before {
    opacity: 1;
}

.sidebar-menu-dark i {
    margin-left: var(--spacing-sm);
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

/* القسم البطولي الاحترافي */
.hero-dark-pro {
    background: var(--gradient-dark);
    color: var(--text-primary);
    padding: calc(var(--spacing-2xl) + 80px) 0 var(--spacing-2xl);
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: 0;
}

.hero-dark-pro::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.hero-content-dark {
    position: relative;
    z-index: 2;
}

.hero-title-dark {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle-dark {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* خدمات الشركة */
.service-card-dark {
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.service-card-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card-dark:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-light);
}

.service-card-dark:hover::before {
    opacity: 1;
}

.service-icon-dark {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 2rem;
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
}

/* التذييل الاحترافي */
.footer-dark-pro {
    background: var(--charcoal-dark);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    color: var(--text-secondary);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    .hero-title-dark { font-size: 2.5rem; }
    .hero-subtitle-dark { font-size: 1.1rem; }
    
    .sidebar-dark-pro {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar-dark-pro.active {
        right: 0;
    }
    
    .card-dark-pro {
        padding: var(--spacing-md);
    }
    
    .service-icon-dark {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* إصلاح المحتوى المقصوص */
.main-content-fixed {
    padding-top: 100px;
}

.section-with-navbar {
    padding-top: 100px;
}

/* معرض الأعمال */
.portfolio-card {
    transition: all 0.3s ease;
    overflow: hidden;
}

.portfolio-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay-content {
    text-align: center;
}

.video-thumbnail {
    position: relative;
    cursor: pointer;
}

.video-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.video-play-overlay:hover {
    background: rgba(0, 0, 0, 0.7);
}

.video-play-btn {
    background: rgba(59, 130, 246, 0.9) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 80px !important;
    height: 80px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
}

.video-play-btn:hover {
    background: var(--accent-blue) !important;
    transform: scale(1.1) !important;
}

.portfolio-video-container {
    position: relative;
    overflow: hidden;
}

/* تحسينات Lightbox */
.lb-data .lb-caption {
    font-family: 'Cairo', sans-serif !important;
    color: #fff !important;
    font-size: 14px !important;
}

.lb-data .lb-number {
    color: #ccc !important;
    font-family: 'Cairo', sans-serif !important;
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.text-primary-dark { color: var(--text-primary); }
.text-secondary-dark { color: var(--text-secondary); }
.text-muted-dark { color: var(--text-muted); }
.text-accent { color: var(--accent-blue); }
