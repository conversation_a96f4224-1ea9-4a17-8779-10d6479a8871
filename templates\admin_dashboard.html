{% extends 'base.html' %}

{% block title %}Admin Dashboard - IQHome{% endblock %}

{% block extra_css %}
<style>
.dashboard-card {
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 300px;
}

.recent-activity {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.activity-item.warning {
    border-left-color: var(--warning-color);
}

.activity-item.danger {
    border-left-color: var(--danger-color);
}

.activity-item.success {
    border-left-color: var(--success-color);
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt me-3"></i>Admin Dashboard</h1>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="exportData()">
            <i class="fas fa-download me-2"></i>Export
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users stat-icon"></i>
                <h3 id="total-users">{{ stats.total_users|default:0 }}</h3>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-microchip stat-icon"></i>
                <h3 id="total-devices">{{ stats.total_devices|default:0 }}</h3>
                <p class="mb-0">Total Devices</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-ticket-alt stat-icon"></i>
                <h3 id="open-tickets">{{ stats.open_tickets|default:0 }}</h3>
                <p class="mb-0">Open Tickets</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-bell stat-icon"></i>
                <h3 id="notifications">{{ stats.notifications|default:0 }}</h3>
                <p class="mb-0">Notifications</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>Device Types Distribution</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="deviceTypesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>User Registration Trend</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="userTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and Quick Actions -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                <a href="/admin/logs/" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="recent-activity" id="recent-activity">
                    <!-- Activity items will be loaded here -->
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/api/admin/users/" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                    <a href="/api/devices/" class="btn btn-outline-success">
                        <i class="fas fa-microchip me-2"></i>Monitor Devices
                    </a>
                    <a href="/api/tickets/" class="btn btn-outline-warning">
                        <i class="fas fa-ticket-alt me-2"></i>Support Tickets
                    </a>
                    <a href="/api/notifications/" class="btn btn-outline-info">
                        <i class="fas fa-bell me-2"></i>Send Notification
                    </a>
                    <hr>
                    <a href="/admin/" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>Django Admin
                    </a>
                    <a href="/swagger/" class="btn btn-outline-dark">
                        <i class="fas fa-code me-2"></i>API Documentation
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>System Alerts</h5>
            </div>
            <div class="card-body">
                <div id="system-alerts">
                    <!-- System alerts will be loaded here -->
                    <p class="text-muted">No alerts at this time</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Dashboard functionality
let deviceTypesChart, userTrendChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadRecentActivity();
    loadSystemAlerts();
    
    // Auto-refresh every 5 minutes
    setInterval(refreshDashboard, 300000);
});

function initializeCharts() {
    // Device Types Chart
    const deviceCtx = document.getElementById('deviceTypesChart').getContext('2d');
    deviceTypesChart = new Chart(deviceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Smart Lights', 'Sensors', 'Cameras', 'Thermostats', 'Others'],
            datasets: [{
                data: [30, 25, 20, 15, 10],
                backgroundColor: [
                    '#007BFF',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // User Trend Chart
    const userCtx = document.getElementById('userTrendChart').getContext('2d');
    userTrendChart = new Chart(userCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 15, 25, 22, 30],
                borderColor: '#007BFF',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadRecentActivity() {
    // Simulate loading recent activity
    const activityContainer = document.getElementById('recent-activity');
    
    setTimeout(() => {
        activityContainer.innerHTML = `
            <div class="activity-item">
                <small class="text-muted">2 minutes ago</small>
                <p class="mb-1">New user registered: john_doe</p>
            </div>
            <div class="activity-item warning">
                <small class="text-muted">5 minutes ago</small>
                <p class="mb-1">Device offline: Living Room Light</p>
            </div>
            <div class="activity-item success">
                <small class="text-muted">10 minutes ago</small>
                <p class="mb-1">Support ticket resolved: #IQHOME-20250729-0001</p>
            </div>
            <div class="activity-item">
                <small class="text-muted">15 minutes ago</small>
                <p class="mb-1">New device added: Kitchen Sensor</p>
            </div>
        `;
    }, 1000);
}

function loadSystemAlerts() {
    // Simulate loading system alerts
    const alertsContainer = document.getElementById('system-alerts');
    
    setTimeout(() => {
        alertsContainer.innerHTML = `
            <div class="alert alert-warning alert-sm">
                <i class="fas fa-exclamation-triangle me-2"></i>
                3 devices offline
            </div>
            <div class="alert alert-info alert-sm">
                <i class="fas fa-info-circle me-2"></i>
                System backup completed
            </div>
        `;
    }, 1500);
}

function refreshDashboard() {
    // Refresh all dashboard data
    loadRecentActivity();
    loadSystemAlerts();
    
    // Show refresh indicator
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const icon = refreshBtn.querySelector('i');
    icon.classList.add('fa-spin');
    
    setTimeout(() => {
        icon.classList.remove('fa-spin');
    }, 2000);
}

function exportData() {
    // Simulate data export
    alert('Export functionality will be implemented');
}
</script>
{% endblock %}
