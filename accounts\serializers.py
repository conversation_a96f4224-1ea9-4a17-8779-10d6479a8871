from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User
class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration

    This serializer handles new user registration with password validation
    and confirmation. It creates a new user account with the provided information.
    """
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        help_text="Password must meet security requirements"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Must match the password field"
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password_confirm', 'first_name', 'last_name', 'phone')
        extra_kwargs = {
            'username': {'help_text': 'Unique username for login'},
            'email': {'help_text': 'Valid email address'},
            'first_name': {'help_text': 'User first name'},
            'last_name': {'help_text': 'User last name'},
            'phone': {'help_text': 'Phone number (optional)'},
        }
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user


class UserProfileSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'phone', 
                 'role', 'is_verified', 'profile_picture', 'address', 'full_name', 'created_at')
        read_only_fields = ('id', 'username', 'role', 'is_verified', 'created_at')


class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'phone', 'profile_picture', 'address')


class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # Add custom claims
        token['username'] = user.username
        token['email'] = user.email
        token['role'] = user.role
        token['is_admin'] = user.is_admin
        
        return token
    
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add user information to response
        data['user'] = {
            'id': self.user.id,
            'username': self.user.username,
            'email': self.user.email,
            'role': self.user.role,
            'is_admin': self.user.is_admin,
            'full_name': self.user.full_name,
        }
        
        return data


class AdminUserListSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'phone',
                 'role', 'is_active', 'is_verified', 'full_name', 'device_count', 'created_at')
    
    def get_device_count(self, obj):
        return obj.devices.count()


class AdminUserDetailSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    device_count = serializers.SerializerMethodField()
    recent_activity = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'phone',
                 'role', 'is_active', 'is_verified', 'profile_picture', 'address',
                 'full_name', 'device_count', 'recent_activity', 'created_at', 'last_login')
    
    def get_device_count(self, obj):
        return obj.devices.count()
    
    def get_recent_activity(self, obj):
        # Get recent system logs for this user
        from notifications.models import SystemLog
        recent_logs = SystemLog.objects.filter(performed_by=obj).order_by('-timestamp')[:5]
        return [
            {
                'action': log.get_action_display(),
                'timestamp': log.timestamp,
                'description': log.description
            }
            for log in recent_logs
        ]


# Simple authentication functions for now (without DRF)
def authenticate_user(username, password):
    """
    Simple authentication function
    """
    user = authenticate(username=username, password=password)
    return user


def create_user(username, email, password, **extra_fields):
    """
    Create a new user
    """
    try:
        validate_password(password)
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            **extra_fields
        )
        return user, None
    except ValidationError as e:
        return None, str(e)
    except Exception as e:
        return None, str(e)
