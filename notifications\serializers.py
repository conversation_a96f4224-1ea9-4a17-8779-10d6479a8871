from rest_framework import serializers
from .models import Notification, SystemLog


class NotificationSerializer(serializers.ModelSerializer):
    """
    Serializer for Notifications
    """
    target_user_name = serializers.CharField(source='target_user.username', read_only=True)
    related_device_name = serializers.CharField(source='related_device.name', read_only=True)
    related_ticket_number = serializers.CharField(source='related_ticket.ticket_number', read_only=True)
    is_expired = serializers.ReadOnlyField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'target_user',
            'target_user_name', 'status', 'is_broadcast', 'created_at',
            'read_at', 'expires_at', 'related_device', 'related_device_name',
            'related_ticket', 'related_ticket_number', 'extra_data', 'is_expired'
        ]
        read_only_fields = ['id', 'created_at', 'read_at']


class NotificationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating notifications (admin only)
    """
    
    class Meta:
        model = Notification
        fields = [
            'title', 'message', 'notification_type', 'target_user',
            'is_broadcast', 'expires_at', 'related_device',
            'related_ticket', 'extra_data'
        ]
    
    def validate(self, attrs):
        """Validate notification data"""
        if attrs.get('is_broadcast') and attrs.get('target_user'):
            raise serializers.ValidationError(
                "Broadcast notifications cannot have a specific target user"
            )
        
        if not attrs.get('is_broadcast') and not attrs.get('target_user'):
            raise serializers.ValidationError(
                "Non-broadcast notifications must have a target user"
            )
        
        return attrs


class SystemLogSerializer(serializers.ModelSerializer):
    """
    Serializer for System Logs
    """
    performed_by_name = serializers.CharField(source='performed_by.username', read_only=True)
    
    class Meta:
        model = SystemLog
        fields = [
            'id', 'action', 'level', 'description', 'performed_by',
            'performed_by_name', 'timestamp', 'ip_address',
            'user_agent', 'extra_data'
        ]
        read_only_fields = ['id', 'timestamp']


class SystemLogCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating system logs
    """
    
    class Meta:
        model = SystemLog
        fields = [
            'action', 'level', 'description', 'performed_by',
            'ip_address', 'user_agent', 'extra_data'
        ]


class NotificationStatsSerializer(serializers.Serializer):
    """
    Serializer for notification statistics
    """
    total_notifications = serializers.IntegerField()
    unread_notifications = serializers.IntegerField()
    read_notifications = serializers.IntegerField()
    notifications_by_type = serializers.DictField()
    recent_notifications = serializers.ListField()


class UserNotificationSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for user notifications
    """
    related_device_name = serializers.CharField(source='related_device.name', read_only=True)
    related_ticket_number = serializers.CharField(source='related_ticket.ticket_number', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'status',
            'created_at', 'read_at', 'expires_at', 'related_device_name',
            'related_ticket_number'
        ]
        read_only_fields = ['id', 'created_at', 'read_at']


class BulkNotificationActionSerializer(serializers.Serializer):
    """
    Serializer for bulk notification actions
    """
    notification_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    action = serializers.ChoiceField(choices=['mark_read', 'archive', 'delete'])
    
    def validate_notification_ids(self, value):
        """Validate that all notification IDs exist and belong to the user"""
        user = self.context['request'].user
        
        if user.is_admin:
            existing_notifications = Notification.objects.filter(id__in=value)
        else:
            existing_notifications = Notification.objects.filter(
                id__in=value,
                target_user=user
            )
        
        existing_ids = set(existing_notifications.values_list('id', flat=True))
        provided_ids = set(value)
        
        if existing_ids != provided_ids:
            missing_ids = provided_ids - existing_ids
            raise serializers.ValidationError(
                f"Some notification IDs not found or not accessible: {missing_ids}"
            )
        
        return value
