from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class SupportTicket(models.Model):
    """
    Support ticket model for user requests and technical support
    """

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('waiting_user', 'Waiting for User'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    CATEGORY_CHOICES = [
        ('technical', 'Technical Issue'),
        ('device', 'Device Problem'),
        ('account', 'Account Issue'),
        ('billing', 'Billing Question'),
        ('feature', 'Feature Request'),
        ('bug', 'Bug Report'),
        ('other', 'Other'),
    ]

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket_number = models.Char<PERSON>ield(max_length=20, unique=True, editable=False)

    # Ticket details
    title = models.CharField(max_length=200)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='technical')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')

    # User information
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='support_tickets')
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tickets',
        limit_choices_to={'role': 'admin'}
    )

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(blank=True, null=True)
    closed_at = models.DateTimeField(blank=True, null=True)

    # Additional information
    attachments = models.FileField(upload_to='support_attachments/', blank=True, null=True)
    device_related = models.ForeignKey(
        'devices.Device',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='support_tickets'
    )

    class Meta:
        db_table = 'support_tickets'
        verbose_name = 'Support Ticket'
        verbose_name_plural = 'Support Tickets'
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            # Generate ticket number: IQHOME-YYYYMMDD-XXXX
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
            last_ticket = SupportTicket.objects.filter(
                ticket_number__startswith=f'IQHOME-{date_str}'
            ).order_by('-ticket_number').first()

            if last_ticket:
                last_num = int(last_ticket.ticket_number.split('-')[-1])
                new_num = last_num + 1
            else:
                new_num = 1

            self.ticket_number = f'IQHOME-{date_str}-{new_num:04d}'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.ticket_number} - {self.title}"

    @property
    def is_open(self):
        """Check if ticket is still open"""
        return self.status in ['open', 'in_progress', 'waiting_user']

    def close_ticket(self, resolved=True):
        """Close the ticket"""
        if resolved:
            self.status = 'resolved'
            self.resolved_at = timezone.now()
        else:
            self.status = 'closed'
        self.closed_at = timezone.now()
        self.save(update_fields=['status', 'resolved_at', 'closed_at'])


class TicketMessage(models.Model):
    """
    Messages/replies within a support ticket
    """

    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    message = models.TextField()
    is_internal = models.BooleanField(default=False)  # Internal admin notes

    created_at = models.DateTimeField(default=timezone.now)

    # Attachments for individual messages
    attachment = models.FileField(upload_to='ticket_messages/', blank=True, null=True)

    class Meta:
        db_table = 'ticket_messages'
        verbose_name = 'Ticket Message'
        verbose_name_plural = 'Ticket Messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message in {self.ticket.ticket_number} by {self.sender.username}"
