from django.contrib import admin
from .models import Notification, SystemLog


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Notification admin interface
    """

    list_display = ('title', 'target_user_display', 'notification_type', 'status', 'is_broadcast', 'created_at')
    list_filter = ('notification_type', 'status', 'is_broadcast', 'created_at')
    search_fields = ('title', 'message', 'target_user__username')
    ordering = ('-created_at',)

    fieldsets = (
        ('Notification Content', {
            'fields': ('title', 'message', 'notification_type')
        }),
        ('Target and Status', {
            'fields': ('target_user', 'is_broadcast', 'status')
        }),
        ('Related Objects', {
            'fields': ('related_device', 'related_ticket'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'read_at', 'expires_at'),
            'classes': ('collapse',)
        }),
        ('Additional Data', {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'read_at')

    def target_user_display(self, obj):
        """Display target user or 'Broadcast'"""
        return obj.target_user.username if obj.target_user else 'Broadcast'
    target_user_display.short_description = 'Target User'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('target_user', 'related_device', 'related_ticket')


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    """
    System log admin interface
    """

    list_display = ('action', 'level', 'performed_by_display', 'timestamp', 'description_preview')
    list_filter = ('action', 'level', 'timestamp')
    search_fields = ('action', 'description', 'performed_by__username')
    ordering = ('-timestamp',)

    fieldsets = (
        ('Log Entry', {
            'fields': ('action', 'level', 'description', 'performed_by')
        }),
        ('Request Information', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Timestamp', {
            'fields': ('timestamp',)
        }),
        ('Additional Data', {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('timestamp',)

    def performed_by_display(self, obj):
        """Display user who performed action or 'System'"""
        return obj.performed_by.username if obj.performed_by else 'System'
    performed_by_display.short_description = 'Performed By'

    def description_preview(self, obj):
        """Show a preview of the description"""
        return obj.description[:100] + '...' if len(obj.description) > 100 else obj.description
    description_preview.short_description = 'Description Preview'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('performed_by')

    def has_add_permission(self, request):
        """Prevent manual addition of system logs"""
        return False

    def has_change_permission(self, request, obj=None):
        """Prevent modification of system logs"""
        return False
