from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from .models import User
from .serializers import authenticate_user, create_user


def home_view(request):
    """
    Company home page view - Arabic luxury design
    """
    return render(request, 'company_home.html')


# Traditional Django views for authentication
def login_view(request):
    """
    Login view for both admin and user portals
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate_user(username, password)
        if user:
            login(request, user)

            # Redirect based on user role
            if user.is_admin:
                return redirect('/admin/dashboard/')
            else:
                return redirect('/dashboard/')
        else:
            messages.error(request, 'Invalid username or password')

    return render(request, 'accounts/login.html')


def admin_login_view(request):
    """
    Admin-specific login view
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate_user(username, password)
        if user and user.is_admin:
            login(request, user)
            return redirect('/admin/dashboard/')
        else:
            messages.error(request, 'Invalid admin credentials')

    return render(request, 'accounts/admin_login.html')


def register_view(request):
    """
    User registration view
    """
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        password_confirm = request.POST.get('password_confirm')
        first_name = request.POST.get('first_name', '')
        last_name = request.POST.get('last_name', '')
        phone = request.POST.get('phone', '')

        if password != password_confirm:
            messages.error(request, "Passwords don't match")
            return render(request, 'accounts/register.html')

        user, error = create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            phone=phone
        )

        if user:
            messages.success(request, 'Account created successfully! Please log in.')
            return redirect('/login/')
        else:
            messages.error(request, f'Registration failed: {error}')

    return render(request, 'accounts/register.html')


@login_required
def logout_view(request):
    """
    Logout view
    """
    logout(request)
    return redirect('/login/')


@login_required
def profile_view(request):
    """
    User profile view
    """
    if request.method == 'POST':
        user = request.user
        user.first_name = request.POST.get('first_name', user.first_name)
        user.last_name = request.POST.get('last_name', user.last_name)
        user.phone = request.POST.get('phone', user.phone)
        user.address = request.POST.get('address', user.address)

        if 'profile_picture' in request.FILES:
            user.profile_picture = request.FILES['profile_picture']

        user.save()
        messages.success(request, 'Profile updated successfully!')

    return render(request, 'accounts/profile.html', {'user': request.user})


@login_required
def admin_dashboard_view(request):
    """
    Admin dashboard view
    """
    if not request.user.is_admin:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('/login/')

    # Get dashboard statistics
    from devices.models import Device
    from support.models import SupportTicket
    from notifications.models import Notification

    stats = {
        'total_users': User.objects.count(),
        'total_devices': Device.objects.count(),
        'open_tickets': SupportTicket.objects.filter(status__in=['open', 'in_progress', 'waiting_user']).count(),
        'notifications': Notification.objects.filter(status='unread').count(),
    }

    return render(request, 'admin_dashboard.html', {'stats': stats})


@login_required
def user_dashboard_view(request):
    """
    User dashboard view
    """
    # Get user's device statistics
    from devices.models import Device
    from notifications.models import Notification

    user_devices = Device.objects.filter(user=request.user)
    user_notifications = Notification.objects.filter(target_user=request.user, status='unread')

    stats = {
        'total_devices': user_devices.count(),
        'online_devices': user_devices.filter(status='online').count(),
        'energy_usage': '15.2 kWh',  # This would be calculated from device data
        'automation_rules': 0,  # This would be from automation system
    }

    return render(request, 'user_dashboard.html', {'stats': stats})


# API views
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import (
    UserRegistrationSerializer, UserProfileSerializer, UserUpdateSerializer,
    PasswordChangeSerializer, CustomTokenObtainPairSerializer,
    AdminUserListSerializer, AdminUserDetailSerializer
)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token obtain view

    Provides JWT access and refresh tokens for authentication.
    Returns additional user information along with tokens.
    """
    serializer_class = CustomTokenObtainPairSerializer


class UserRegistrationAPIView(generics.CreateAPIView):
    """
    User registration API endpoint

    Allows new users to register for the IQHome platform.
    Creates a new user account with the provided information.

    **Required fields:**
    - username: Unique username
    - email: Valid email address
    - password: Secure password
    - password_confirm: Password confirmation

    **Optional fields:**
    - first_name: User's first name
    - last_name: User's last name
    - phone: Phone number
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]


class UserProfileAPIView(generics.RetrieveUpdateAPIView):
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserUpdateAPIView(generics.UpdateAPIView):
    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password_api(request):
    serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        return Response({'message': 'Password changed successfully'})
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Admin API views
class AdminUserListAPIView(generics.ListAPIView):
    queryset = User.objects.all()
    serializer_class = AdminUserListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if not self.request.user.is_admin:
            return User.objects.none()
        return User.objects.all().order_by('-created_at')


class AdminUserDetailAPIView(generics.RetrieveUpdateAPIView):
    queryset = User.objects.all()
    serializer_class = AdminUserDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if not self.request.user.is_admin:
            return User.objects.none()
        return User.objects.all()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_dashboard_stats_api(request):
    """
    Get comprehensive dashboard statistics for admin
    """
    if not request.user.is_admin:
        return Response({'error': 'Admin access required'}, status=status.HTTP_403_FORBIDDEN)

    from devices.models import Device, ControlCommand
    from support.models import SupportTicket
    from notifications.models import Notification, SystemLog
    from django.utils import timezone
    from datetime import timedelta
    from django.db.models import Count

    # Basic counts
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    total_devices = Device.objects.count()
    online_devices = Device.objects.filter(status='online').count()

    # Support statistics
    total_tickets = SupportTicket.objects.count()
    open_tickets = SupportTicket.objects.filter(status__in=['open', 'in_progress', 'waiting_user']).count()
    resolved_tickets = SupportTicket.objects.filter(status='resolved').count()

    # Notification statistics
    total_notifications = Notification.objects.count()
    unread_notifications = Notification.objects.filter(status='unread').count()

    # Recent activity (last 24 hours)
    yesterday = timezone.now() - timedelta(days=1)
    recent_users = User.objects.filter(created_at__gte=yesterday).count()
    recent_devices = Device.objects.filter(created_at__gte=yesterday).count()
    recent_tickets = SupportTicket.objects.filter(created_at__gte=yesterday).count()
    recent_commands = ControlCommand.objects.filter(timestamp__gte=yesterday).count()

    # Device types breakdown
    device_types = Device.objects.values('device_type').annotate(
        count=Count('device_type')
    ).order_by('-count')

    # Recent system logs
    recent_logs = SystemLog.objects.select_related('performed_by').order_by('-timestamp')[:10]
    recent_activity = [
        {
            'action': log.get_action_display(),
            'description': log.description,
            'user': log.performed_by.username if log.performed_by else 'System',
            'timestamp': log.timestamp,
            'level': log.level
        }
        for log in recent_logs
    ]

    stats_data = {
        'overview': {
            'total_users': total_users,
            'active_users': active_users,
            'total_devices': total_devices,
            'online_devices': online_devices,
            'offline_devices': total_devices - online_devices,
            'total_tickets': total_tickets,
            'open_tickets': open_tickets,
            'resolved_tickets': resolved_tickets,
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
        },
        'recent_activity_counts': {
            'new_users': recent_users,
            'new_devices': recent_devices,
            'new_tickets': recent_tickets,
            'device_commands': recent_commands,
        },
        'device_types': {item['device_type']: item['count'] for item in device_types},
        'recent_activity': recent_activity,
        'system_health': {
            'database_status': 'healthy',
            'api_status': 'operational',
            'last_backup': timezone.now() - timedelta(hours=6),
        }
    }

    return Response(stats_data)
